/**
 * 过滤掉对象中的空值参数
 * @param params 参数对象
 * @returns 过滤后的参数对象
 */
export const filterNullParams = (params: any) => {
  if (typeof params !== 'object' || params === null) {
    return params
  }

  const filteredParams: any = {}
  for (const key in params) {
    if (Object.prototype.hasOwnProperty.call(params, key)) {
      const value = params[key]
      // 过滤掉 null、undefined、空字符串、空数组、空对象
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            filteredParams[key] = value
          }
        } else if (typeof value === 'object') {
          if (Object.keys(value).length > 0) {
            filteredParams[key] = value
          }
        } else {
          filteredParams[key] = value
        }
      }
    }
  }
  return filteredParams
}
