import request from '@/config/axios'

// 国内培训 VO
export interface InternalRespVO {
  id?: number // 主键ID
  code: string // 编号
  title: string // 课程名称
  titleAr: string // 课程名称(阿文)
  type: number // 培训类型(1.Course，2.Training，3.Visit，4.Conference，5.Lecture，6.Meeting，7.Workshop)
  place: string // 培训地点
  startDate: Date // 开始时间
  endDate: Date // 结束时间
  duration: number // 培训时长(days)
  implementingCompany: string // 培训公司
  attachments: string // 附件
  remark: string // 备注
  createTime?: string
}

export interface InternalReqVO {
  pageNo: number
  pageSize: number
  code: string
  title: string
  titleAr: string
  type: number
  place: string
  startTime: string
  endTime: string
}

export interface userInfoVO {
  userId: number
  userNameAr: string
  userGroupNo: string
  status: number
}

export interface InternalUserReqVO {
  pageNo: number
  pageSize: number
  internalId: number
}

export interface InternalSaveVO extends InternalRespVO{
  internalUsers: userInfoVO[]
}

export interface InternalConditionReqVO {
  place: string
  implementingCompany: string
}

// 国内培训 API
export const InternalApi = {
  // 查询国内培训分页
  getInternalPage: async (params: InternalReqVO) => {
    return await request.get({ url: `/academy/internal/page`, params })
  },

  // 查询国内培训详情
  getInternal: async (id: number) => {
    return await request.get({ url: `/academy/internal/get?id=` + id })
  },

  // 新增国内培训
  createInternal: async (data: InternalSaveVO) => {
    return await request.post({ url: `/academy/internal/create`, data })
  },

  // 修改国内培训
  updateInternal: async (data: InternalSaveVO) => {
    return await request.put({ url: `/academy/internal/update`, data })
  },

  // 删除国内培训
  deleteInternal: async (id: number) => {
    return await request.delete({ url: `/academy/internal/delete?id=` + id })
  },

  // 导出国内培训 Excel
  exportInternal: async (params) => {
    return await request.download({ url: `/academy/internal/export-excel`, params })
  },
  // 获得国内培训人员分页
  getInternalUserPage: async (params: InternalUserReqVO) => {
    return await request.get({  url: `/academy/internal-user/page`, params })
  },
  // 删除国内培训人员
  deleteInternalUser: async (id: number) => {
    return await request.delete({ url: `/academy/internal-user/delete?id=` + id })
  },
  // 获取培训编号
  getCode: async () => {
    return await request.get({  url: `/academy/internal/count` })
  }
}
