import request from '@/config/axios'

export interface InternalRespVO {
  pageNo: number
  pageSize: number
  code: string
  title: string
  titleAr: string
  receivingCountry: string
  travelDate: string
  returnDate: string
  adminNo: string
  costBearer: string
}

// 国外培训 VO
export interface ExternalRespVO {
  id: number // 主键ID
  code: string // 编号
  title: string // 课程名称
  titleAr: string // 课程名称(阿文)
  receivingCountry: string // 海外培训地点
  travelDate: Date // 出发时间
  returnDate: Date // 返回时间
  adminNo: string // 行政编号
  costBearer: string // 费用承担公司
  attachments: string // 附件
  remark: string // 备注
}

export interface userItem {
  userId: number
  userNameAr: string
  bocBadgeNo: string
  userGroupNo: string
}
export interface InternalSaveVO {
  id?: number // 主键ID
  code: string // 编号
  title: string // 课程名称
  titleAr: string // 课程名称(阿文)
  receivingCountry: string // 海外培训地点
  travelDate: Date // 出发时间
  returnDate: Date // 返回时间
  adminNo: string // 行政编号
  costBearer: string // 费用承担公司
  attachments: string[] // 附件
  remark: string // 备注
  userList: userItem[]
  createTime?: string
}

export interface ExternalUserReqVO {
  pageNo: number
  pageSize: number
  externalId: number
}
export interface ExternalUserRespVO {
  id: number
  userId: number
  nickname: string
  positionName: string
  deptName: string
  externalId: number
  externalName: string
  receivingCountry: string
  travelCate: Date
  returnDate: Date
  adminNo: string
  costBearer: string
  userNameAr: string
  bocBadgeNo: string
  userGroupNo: string
  createTime: Date
}

// 国外培训 API
export const ExternalApi = {
  // 查询国外培训分页
  getExternalPage: async (params: InternalRespVO) => {
    return await request.get({ url: `/academy/external/page`, params })
  },

  // 查询国外培训详情
  getExternal: async (id: number) => {
    return await request.get({ url: `/academy/external/get?id=` + id })
  },

  // 新增国外培训
  createExternal: async (data: InternalSaveVO) => {
    return await request.post({ url: `/academy/external/create`, data })
  },

  // 修改国外培训
  updateExternal: async (data: InternalSaveVO) => {
    return await request.put({ url: `/academy/external/update`, data })
  },

  // 删除国外培训
  deleteExternal: async (id: number) => {
    return await request.delete({ url: `/academy/external/delete?id=` + id })
  },
  // 获得国外培训人员分页
  getExternalUserPage: async (params: ExternalUserReqVO) => {
    return await request.get<PageResult<ExternalUserRespVO[]>>({ url: `/academy/external-user/page`, params })
  },
  // 删除国外培训人员
  deleteExternalUser: async (id: number) => {
    return await request.delete({ url: `/academy/external-user/delete?id=` + id })
  },
  // 获得国内培训数量
  getCode: async () => {
    return await request.get({ url: `/academy/external/count` })
  }
}
