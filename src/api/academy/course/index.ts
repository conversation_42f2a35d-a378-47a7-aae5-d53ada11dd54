import request from '@/config/axios'
import {AssignFun} from "@/api/topicMgt/elearning";
import {PageResult} from "../../../../types/global";

export interface CourseReqVO {
  pageNo: number
  pageSize: number
  categoryId: number
  title: string
  languages: number[]
  trainerType: string
}


// 学院课程信息 VO
export interface CourseSaveVO {
  id: number // 主键id
  title: string // 课程名称
  // code: string // 课程编号
  status: number // 状态(保留)
  categoryId: number // 课程类别
  examId: number // 考试id(当模版用)
  cover: string // 封面
  languageList: number[] // 语言(1.英文 2.阿文 3.中文)
  trainerType: number // 教师类型(1.3rd 2.satelite 3.MDC 4.HSE 4.Road Safty)
  ifmsApprovalKey: number // 是否开启审批(0.否 1.是)
  contractorApprovalKey: number // 是否开启审批(0.否 1.是)
  approvalConfigId: string // 审批配置id 逗号分隔字符串
  scope: number // 分配范围(1.全公司 2.特定人员)
  validity: number // 证书的效期(month)
  feedbackStatus: number // 是否开启反馈(1.开启 0.未开启)
  feedbackLanguage: number // 反馈语言(1.英语 2.阿拉伯文 3.中文)
  prerequisteCourse: string // 课程条件
  prerequisteAttachment: string // 附件条件
  absentTime: number // 缺席次数
  freezeTime: number // 冻结时间(months)
  certificateId: number // 证书id
  notification: number // 是否开启通知(0.未开启 1.开启)开始前24小时推送通知
  bookingTime: number // 是否开启预定时间(0.未开启 1.开启)开启后，24h之内不可以报名，24h之外可以报名
  remarks: string // 描述
  keywords: string[]
  level: number
}

export interface CourseRespVO extends CourseSaveVO{
  categoryFullPath: string
}

export interface AssignFunSaveVO {
  id: number
  relevanceId: number
  relevanceName: string
  scope: number
  type: number
}

export interface CourseAppReqVO {
  pageNo: number
  pageSize: number
  courseIds: string[] | number
}

// 学院课程信息 API
export const CourseApi = {
  // 查询学院课程信息分页
  getCoursePage: async (params: CourseReqVO) => {
    return await request.get<PageResult<CourseRespVO[]>>({ url: `/academy/course/page`, params })
  },

  // 查询学院课程信息详情
  getCourse: async (id: number) => {
    return await request.get({ url: `/academy/course/get?id=` + id })
  },

  // 新增学院课程信息
  createCourse: async (data: CourseSaveVO) => {
    return await request.post({ url: `/academy/course/create`, data })
  },

  // 修改学院课程信息
  updateCourse: async (data: CourseSaveVO) => {
    return await request.put({ url: `/academy/course/update`, data })
  },

  // 删除学院课程信息
  deleteCourse: async (ids: string[] | number) => {
    return await request.delete({ url: `/academy/course/delete?ids=` + ids })
  },

  // 获取课程编号 Todo 待补充后台接口
  getCourseCode: async () => {
    return await request.get({ url: `/academy/course/getCourseCode` })
  },

  // 查询学院课程已分配列表
  getCourseAssignPage: async (courseId: number) => {
    return await request.get({ url: `/academy/course/assignedList?courseId=` + courseId })
  },
  // 分配学院课程人员
  AssignFun: async (id: number, scope: number, type: any, data: string[]) => {
    return await request.post({ url: `/academy/course/assign?courseId=${id}&scope=${scope}&type=${type}`, data })
  },
  // 批量删除分配学院课程人员
  delAssign: async (ids: string[]) => {
    return await request.delete({ url: `/academy/course/assign/${ids}` })
  }
}
