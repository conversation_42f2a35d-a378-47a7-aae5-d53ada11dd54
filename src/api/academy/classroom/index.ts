import request from '@/config/axios'

export interface ClassRoomReqVO {
  pageNo: number
  pageSize: number
  name: string
  status: string
  location: string
  createTime: string
}
// 教室信息 VO
export interface ClassRoomSaveVO {
  id?: number // 主键id
  name: string // 教室名称
  roomNumber: string // 教室编号
  location: number // 位置(1.FCC 2.MLC 3.PC)
  picture: string // 教室图片
  totalSeats: number // 教室容量
  description: string // 描述
}

export interface ClassRoomRespVO extends ClassRoomSaveVO{
  createTime: string
}

export interface RoomNumberVO {
  value: number
  label: string
}

// 教室信息 API
export const ClassRoomApi = {
  // 查询教室信息分页
  getClassRoomPage: async (params: ClassRoomReqVO) => {
    return await request.get<PageResult<ClassRoomRespVO[]>>({ url: `/academy/class-room/page`, params })
  },

  // 查询教室信息不分页
  getClassRoomList: async () => {
    return await request.get({ url: `/academy/class-room/simple-list` })
  },

  // 查询教室信息详情
  getClassRoom: async (id: number) => {
    return await request.get<IResponse<ClassRoomRespVO>>({ url: `/academy/class-room/get?id=` + id })
  },

  // 新增教室信息
  createClassRoom: async (data: ClassRoomSaveVO) => {
    return await request.post({ url: `/academy/class-room/create`, data })
  },

  // 修改教室信息
  updateClassRoom: async (data: ClassRoomSaveVO) => {
    return await request.put({ url: `/academy/class-room/update`, data })
  },

  // 删除教室信息
  deleteClassRoom: async (id: number) => {
    return await request.delete({ url: `/academy/class-room/delete?id=` + id })
  },
}
