import request from '@/config/axios'
export interface SectionReqVO {
  sectName: string
  sectCode: string
  status: string
  parentId: number
  deptId: number
  deptName: string
  serviceCompanyId: number
}
export interface SectionResqVO {
  sectId: number
  sectName: string
  shortName: string
  remark: string
  status: string
  createTime: Date
  dataSource: string
  createBy: string

}
export interface SectionSaveVO {
  sectId?: number
  type?: number
  deptIds: string[]
  deptName?: string
  menuIds: string[]
  remark: string
  roleId: number
  roleName: string
  roleSort: number
  status: string
  createTime: Date
}
export interface SectionTreeRespVO {
  id: number
  code: string
  label: string
  level: number // 级别（1：公司，2：部门, 3: section）
  shortName: string
  virtualId: number
  children: string
}

// section状态枚举
export enum SectionStatusEnum {
  DISABLE = 1,
  ENABLE = 0
}
/**  查询section列表 */
export const listSection = (params: SectionReqVO) => {
  return request.get<IResponse<SectionResqVO[]>>({ url: '/system/sect', params })
}

/** 查询section详细 */
export const getSection = (sectId: number) => {
  return request.get({ url: `/system/sect/${sectId}` })
}

/** 新增section */
export const addSection = (data: SectionSaveVO) => {
  return request.post({ url: '/system/sect', data})
}

/** 修改section */
export const updateSection = (data: SectionSaveVO) => {
  return request.put({ url: '/system/sect', data})
}

/** 删除section */
export const delSection = (postId: number) => {
  return request.delete({ url: `/system/sect/${postId}`})
}
/** 获取section 的option(该方法未在项目中使用) */
export const getSectionOption = () => {
  return request.get({ url: '/adapter/v1/sect/option' })
}
/** 查询部门列表（排除节点） */
export const listSectExcludeChild = (deptId: number, sectId: number) => {
  return request.get({ url: `/system/sect/exclude/${deptId}/${sectId}` })
}
// 查询section下拉树结构
export const sectTreeSelect = (params: { type: number,status: number }) => {
  return request.get<IResponse<SectionTreeRespVO[]>>({ url: `/system/sect/tree`, params })
}
