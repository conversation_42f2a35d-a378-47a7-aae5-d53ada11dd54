import request from '@/config/axios'

export interface DeptReqVO {
  deptName: string
  pageNum: number
  pageSize: number
}

export interface DeptRespVO {
  id: number
  name: string
  fullName: string
  parentId: number
  sort: number
  leaderUserId: number
  phone: number
  email: string
  adDepartmentId: string
  status: number
  createTime: Date
  ruleName: string
  rule: string
  companyId: number
  serviceCompanyId: number
  deptCode: string
  level: number
  type: string
}

export interface DeptSaveVO {
  deptId?: number
  companyId: number
  deptCode: string
  deptName: string
  orderNum: number
  parentId: number
  shortName: string
  status: string
}

// 部门状态枚举
export enum DepartmentStatusEnum {
  DISABLE = 1, // 禁用
  ENABLE = 0 // 启用
}

// 查询部门（精简)列表
export const getSimpleDeptList = async () => {
  return await request.get({ url: '/system/dept/simple-list' })
}

// 查询部门列表
export const listDept = (params?: DeptReqVO) => {
  return request.get<IResponse<DeptRespVO[]>>({ url: '/system/dept',params})
}

// 查询部门列表（排除节点）
export const listDeptExcludeChild = (companyId: number, deptId: number) => {
  return request.get({ url: `/system/dept/exclude/${companyId}/${deptId}` })
}

// 查询部门详细
export const getDept = (deptId: number) =>  {
  return request.get<IResponse<DeptRespVO>>({ url: `/system/dept/${deptId}` })
}

// 新增部门
export const addDept = (data: DeptSaveVO) => {
  return request.post({ url: '/system/dept', data})
}

// 修改部门
export const updateDept = (data: DeptSaveVO) => {
  return request.put({ url: '/system/dept', data})
}

// 删除部门
export const delDept = (deptId: number) => {
  return request.delete({ url: `/system/dept/delete?id=` + deptId })
}
