import request from '@/config/axios'

export interface CertificateTemplateReqVO {
  pageNo: number
  pageSize: number
  name: string
  createTime: string
}
// 证书模板 VO
export interface CertificateTemplateRespVO {
  id: number // 主键id
  name: string // 证书模版名称
  image: string // 证书模版图片
  coordinate: string // 证书坐标
  createTime: string
}

export interface CertificateTemplateSaveVO {
  id?: number
  name: string
  image: string
}

// 证书模板 API
export const CertificateTemplateApi = {
  // 查询证书模板分页
  getCertificateTemplatePage: async (params: CertificateTemplateReqVO) => {
    return await request.get({ url: `/system/certificate-template/page`, params })
  },

  // 查询证书模板详情
  getCertificateTemplate: async (id: number) => {
    return await request.get({ url: `/system/certificate-template/get?id=` + id })
  },

  // 新增证书模板
  createCertificateTemplate: async (data: CertificateTemplateSaveVO) => {
    return await request.post({ url: `/system/certificate-template/create`, data })
  },

  // 修改证书模板
  updateCertificateTemplate: async (data: CertificateTemplateSaveVO) => {
    return await request.put({ url: `/system/certificate-template/update`, data })
  },

  // 删除证书模板
  deleteCertificateTemplate: async (id: number) => {
    return await request.delete({ url: `/system/certificate-template/delete?id=` + id })
  },

  // 导出证书模板 Excel
  exportCertificateTemplate: async (params) => {
    return await request.download({ url: `/system/certificate-template/export-excel`, params })
  }
}
