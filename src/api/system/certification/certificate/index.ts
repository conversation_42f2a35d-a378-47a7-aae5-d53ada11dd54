import request from '@/config/axios'
import {PageResult} from "../../../../../types/global";

export interface CertificateReqVO {
  pageNo: number
  pageSize: number
  name: string
  status: number
  createTime: string
}
// 证书 VO
export interface CertificateRespVO {
  id: number // 主键id
  tempId: number // 证书模版ID
  name: string // 证书名称
  numberPrefix: string // 证书编号前缀
  logo: string // logo
  officialSeal: string // 公章
  status: number // 状态 1:启用 2:禁用
  createTime: string
}

export interface CertificateSaveVO {
  id?: number
  tempId: number
  name: string
  numberPrefix: string
  logo: string
  officialSeal: string
  status: number
}

/**
 * 证书状态枚举 	状态 1:启用 2:禁用
 */
export enum CertificateStatusEnum {
  Enable = 1,
  Disable = 2
}

// 证书 API
export const CertificateApi = {
  // 查询证书分页
  getCertificatePage: async (params: CertificateReqVO) => {
    return await request.get<PageResult<CertificateRespVO[]>>({ url: `/system/certificate/page`, params })
  },

  // 查询证书详情
  getCertificate: async (id: number) => {
    return await request.get({ url: `/system/certificate/get?id=` + id })
  },

  // 新增证书
  createCertificate: async (data: CertificateSaveVO) => {
    return await request.post({ url: `/system/certificate/create`, data })
  },

  // 修改证书
  updateCertificate: async (data: CertificateSaveVO) => {
    return await request.put({ url: `/system/certificate/update`, data })
  },

  // 删除证书
  deleteCertificate: async (id: number) => {
    return await request.delete({ url: `/system/certificate/delete?id=` + id })
  },

  // 导出证书 Excel
  exportCertificate: async (params) => {
    return await request.download({ url: `/system/certificate/export-excel`, params })
  }
}
