import request from '@/config/axios'
import { parseStrEmpty } from '@/utils/ruoyi'

export interface UserReqVO {
  pageNo: number
  pageSize: number
  nickName: string,
  phone: number,
  status: string,
  deptId: number,
  nationalityCode: string,
  badgeNumber: string,
  workTerms: string,
  userStatus: string,
}
export interface UserRespVO {
  id?: number
  nickname: string
  username: string
  avatar: string
  badgeNumber: string
  companyId: number
  companyName: string
  dataSource: string
  delFlag: string
  deptId: number
  deptName: string
  email: string
  nationalityCode: string
  nickName: string
  password: string
  phone: string
  postNames: string
  remark: string
  roleNames: string
  sex: string
  status: string | number
  userId: number
  userStatus : string | number
  userName: string
  workTerms: string
  workType: string | number
}

export interface UserSaveVO {
  userId?: number
  nickName: string
  companyId: number
  deptId: number
  sectIds: string[]
  postIds: string[]
  email: string
  badgeNumber: string
  lineManagerIds :string[]
  nationalityCode: string
  phone: string
  workType: string
  status: string
  userStatus: string
  onboardingDate: Date
  workTerms: string
  isSend: string
}

export interface DepartmentTreeReqVO {
  fullName: string
  name: string
  status: number
  deptId: number
  companyId: number
  serviceCompanyId: number
  level: number
  type: number
  deptCode: string
  deptName: string
  shortName: string
  parentId: number
  ancestors: string
  orderNum: number
  delFlag: number
  dataSource: string
  children: string
}

export interface DepartmentTreeRespVO {
  id: number
  code: string
  label: string
  level: number // 级别（1：公司，2：部门, 3: section）
  shortName: string
  virtualId: number
  children: string
}

// 查询用户列表
export const listUser = (params: UserReqVO) => {
  return request.get<PageResult<UserRespVO[]>>({ url: '/system/user/page', params })
}

// 查询用户详细
export const getUser = (id?: number) => {
  return request.get({  url: `/system/user/id/${id}` })
}

// 新增用户
export const addUser = (data: UserSaveVO) => {
  return request.post({ url: '/system/user/create', data})
}

// 修改用户
export const updateUser = (data: UserSaveVO) => {
  return request.put({ url: '/system/user/update', data})
}

// 删除用户
export const delUser = (ids: number) => {
    return request.delete({ url: `/system/user/delete?ids=` + ids
  })
}

// 用户密码重置
export const resetUserPwd = (id: number) => {
  const data = {
    id,
  }
  return request.put({ url: '/system/user/update-password', data })
}

// 用户状态修改
export const changeUserStatus = (id: number, status: string | number) => {
  const data = {
    id,
    status,
  }
  return request.put({ url: '/system/user/update-status', data })
}

// 查询用户个人信息
export const getUserProfile = () => {
  return request.get({ url: '/system/user/profile/get'})
}


// 用户密码重置
export const updateUserPwd = (oldPassword: string, newPassword: string) => {
  const data = {
    oldPassword,
    newPassword,
  }
  return request.put({ url: '/system/user/profile/update-password', data })
}

// 查询授权角色 (未查询到该方法使用)
export const getAuthRole = (userId: number) => {
  return request.get({ url: `/adapter/v1/user/role/${userId}`})
}

// 查询部门下拉树结构
export const deptTreeSelect = (params: DepartmentTreeReqVO) => {
  return request.get<IResponse<DepartmentTreeRespVO[]>>({ url: '/system/dept/tree', params})
}
// 获取用户列表-Assign
export const employeeData = (params: UserReqVO) => {
  return request.get<PageResult<UserRespVO[]>>({ url: '/system/user/page', params})
}
/** 查询lineManageer */
export const lineManagerSelect = () => {
  return request.post({ url: '/system/user/line/manager/list' })
}

/** 查询员工学习统计 */
export const userLearningStatistics = (params: any) => {
  return request.get({ url: '/system/user/learning-statistics', params})
}

/** 查询公司员工统计信息 */
export const companyEmployeeStatistics = () => {
  return request.get({ url: '/system/user/company-employee-statistics'})
}

// 导出用户信息
export const exportUser = (params: UserReqVO) => {
  return request.download({ url: '/system/user/export', params })
}
// 导出
export const exportUserLearningStatistics = (params: any) => {
  return request.download({ url: '/system/user/learning-statistics/export', params })
}


// 获取用户精简信息列表
export const getSimpleUserList = (): Promise<UserRespVO[]> => {
  return request.get({ url: '/system/user/simple-list' })
}


