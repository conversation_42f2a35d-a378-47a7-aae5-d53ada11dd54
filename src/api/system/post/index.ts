import request from '@/config/axios'

export interface PostReqVO {
  postCode: string
  postName: string
  status: string
  parentId: number
  deptId: number
  deptName: string
}

export interface PostRespVO {
  id: number
  name: string
  code: string
  sort: number
  status: number
  remark: string
  createTime: Date
  compId: number
  companyName: string
  comShortname: string
  deptId: number
  deptName: string
  depShortname: string
  sectId: number
  sectName: string
  shortName: string
  parentId: number
  ancestors: string
  dataSource: string
}

export interface PostSaveVO {
  deptIds: string[]
  deptName: string
  sectId: string
  parentId?: number
  postId?: number
  status: string
}

/**  查询Position列表 */
export const listPosition = (params: PostReqVO) => {
  return request.get<IResponse<PostRespVO[]>>({ url: '/system/post', params })
}

/** 查询Position详细 */
export const getPosition = (postId: number) => {
  return request.get({ url: `/system/post/${postId}` })
}

/** 新增Position */
export const addPosition = (data: PostSaveVO) => {
  return request.post({ url: '/system/post', data})
}

/** 修改Position */
export const updatePosition = (data: PostSaveVO) => {
  return request.put({ url: '/system/post', data})
}

/** 删除Position */
export const delPosition = (postId: number) => {
  return request.delete({ url: `/system/post/${postId}`})
}
/** 获取Position 的option(该方法未在项目中使用) */
export const getPositionOption = () => {
  return request.get({ url: '/adapter/v1/post/option' })
}
/** 查询部门列表（排除节点） */
export const listPostExcludeChild = (sectId: number, postId: number) => {
  return request.get({ url: `/system/post/exclude/${sectId}/${postId}` })
}

