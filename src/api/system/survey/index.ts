// 导出所有类型定义
export * from './types'

// 导出API
export { CategoryApi } from './category'
export { TemplateApi } from './template'
export { QuestionApi } from './question'
export { InstanceApi } from './instance'
export { ResponseApi } from './response'
export { StatisticsApi } from './statistics'

// 导出工具函数
export const SurveyUtils = {
  // 获取问题类型名称
  getQuestionTypeName: (type: number): string => {
    const types = {
      1: 'Single Choice',
      2: 'Multiple Choice',
      3: 'True/False',
      4: 'Rating',
      5: 'File Upload',
      6: 'Text'
    }
    return types[type] || 'Unknown'
  },

  // 获取问题类型颜色
  getQuestionTypeColor: (type: number): string => {
    const colors = {
      1: 'primary',
      2: 'success',
      3: 'warning',
      4: 'danger',
      5: 'info',
      6: ''
    }
    return colors[type] || ''
  },

  // 获取通用状态名称（分类和模板使用）
  getCommonStatusName: (status: number): string => {
    const statuses = {
      0: 'Enabled',
      1: 'Disabled'
    }
    return statuses[status] || 'Unknown'
  },

  // 获取通用状态颜色（分类和模板使用）
  getCommonStatusColor: (status: number): string => {
    const colors = {
      0: 'success',
      1: 'danger'
    }
    return colors[status] || 'info'
  },

  // 获取模板状态名称（兼容性方法）
  getTemplateStatusName: (status: number): string => {
    return SurveyUtils.getCommonStatusName(status)
  },

  // 获取模板状态颜色（兼容性方法）
  getTemplateStatusColor: (status: number): string => {
    return SurveyUtils.getCommonStatusColor(status)
  },

  // 获取实例状态名称
  getInstanceStatusName: (status: number): string => {
    const statuses = {
      0: 'Unpublished',
      1: 'Ongoing',
      2: 'Ended',
      3: 'Draft'
    }
    return statuses[status] || 'Unknown'
  },

  // 获取实例状态颜色
  getInstanceStatusColor: (status: number): string => {
    const colors = {
      0: 'info',
      1: 'success',
      2: 'warning',
      3: 'default'
    }
    return colors[status] || 'info'
  },

  // 获取作用域类型名称
  getScopeTypeName: (type: number): string => {
    const types = {
      1: 'Public',
      2: 'Internal'
    }
    return types[type] || 'Unknown'
  },

  // 获取作用域目标类型名称
  getScopeTargetTypeName: (type: number): string => {
    const types = {
      1: 'User',
      2: 'Department'
    }
    return types[type] || 'Unknown'
  },

  // 获取提交频率名称
  getSubmissionFrequencyName: (frequency: number): string => {
    const frequencies = {
      1: 'Once Only',
      2: 'Multiple Times',
      3: 'Unlimited'
    }
    return frequencies[frequency] || 'Unknown'
  },

  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 格式化时长
  formatDuration: (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds} seconds`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes} minutes`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours} hours`
    }
  },

  // 验证问题配置
  validateQuestionConfig: (questionType: number, config: any): { valid: boolean; errors: string[] } => {
    const errors: string[] = []

    switch (questionType) {
      case 1: // Single Choice
      case 2: // Multiple Choice
        if (!config.options || config.options.length < 2) {
          errors.push('At least 2 options are required')
        }
        if (questionType === 2 && config.maxSelections && config.maxSelections > config.options?.length) {
          errors.push('Maximum selections cannot exceed the number of options')
        }
        break
      case 4: // Rating
        if (config.minRating >= config.maxRating) {
          errors.push('Minimum rating must be less than maximum rating')
        }
        break
      case 5: // File Upload
        if (config.maxFileSize && config.maxFileSize <= 0) {
          errors.push('Maximum file size must be greater than 0')
        }
        if (config.maxFileCount && config.maxFileCount <= 0) {
          errors.push('Maximum file count must be greater than 0')
        }
        break
      case 6: // Text
        if (config.minLength && config.maxLength && config.minLength > config.maxLength) {
          errors.push('Minimum length cannot be greater than maximum length')
        }
        break
    }

    return {
      valid: errors.length === 0,
      errors
    }
  },

  // 生成问题默认配置
  getDefaultQuestionConfig: (questionType: number): any => {
    switch (questionType) {
      case 1: // Single Choice
        return {
          options: [
            { value: '1', text: 'Option 1' },
            { value: '2', text: 'Option 2' }
          ],
          allowOther: false
        }
      case 2: // Multiple Choice
        return {
          options: [
            { value: '1', text: 'Option 1' },
            { value: '2', text: 'Option 2' }
          ],
          allowOther: false,
          minSelections: 1,
          maxSelections: undefined
        }
      case 3: // True/False
        return {
          trueText: 'True',
          falseText: 'False'
        }
      case 4: // Rating
        return {
          minRating: 1,
          maxRating: 5,
          ratingType: 'star',
          ratingLabels: ['Very Poor', 'Excellent']
        }
      case 5: // File Upload
        return {
          fileTypes: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
          maxFileSize: 10 * 1024 * 1024, // 10MB
          maxFileCount: 1
        }
      case 6: // Text
        return {
          inputType: 'textarea',
          maxLength: 500,
          placeholder: 'Please enter your answer'
        }
      default:
        return {}
    }
  }
}
