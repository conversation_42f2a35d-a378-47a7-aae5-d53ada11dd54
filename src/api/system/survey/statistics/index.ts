import request from '@/config/axios'
import type {
  SurveyStatistics,
  StatisticsQuery
} from '../types'

export const StatisticsApi = {
  // 获取问卷统计数据
  get: async (params: StatisticsQuery): Promise<SurveyStatistics> => {
    return await request.get({
      url: '/system/survey/statistics/get',
      params
    })
  },

  // 获取问卷部门分布统计
  getDeptDistribution: async (instanceId: number): Promise<any[]> => {
    return await request.get({
      url: '/system/survey/statistics/dept-distribution',
      params: { instanceId }
    })
  },

  // 获取问卷时间分布统计
  getTimeDistribution: async (params: {
    instanceId: number
    timeType?: string
    startTime?: string
    endTime?: string
  }): Promise<any[]> => {
    return await request.get({
      url: '/system/survey/statistics/time-distribution',
      params
    })
  },

  // 获取用户排名
  getUserRank: async (instanceId: number, userId: number): Promise<number> => {
    return await request.get({
      url: '/system/survey/statistics/user-rank',
      params: { instanceId, userId }
    })
  },

  // 刷新问卷统计缓存
  refresh: async (instanceId: number): Promise<boolean> => {
    return await request.post({
      url: '/system/survey/statistics/refresh',
      params: { instanceId }
    })
  },

  // 批量刷新问卷统计缓存
  refreshBatch: async (instanceIds: number[]): Promise<boolean> => {
    return await request.post({
      url: '/system/survey/statistics/refresh-batch',
      data: instanceIds
    })
  },

  // 刷新问卷统计缓存（带选项）
  refreshWithOptions: async (instanceId: number, preWarm?: boolean): Promise<boolean> => {
    return await request.post({
      url: '/system/survey/statistics/refresh-with-options',
      params: { instanceId, preWarm }
    })
  },

  // 清除所有统计缓存
  clearAllCache: async (): Promise<boolean> => {
    return await request.post({
      url: '/system/survey/statistics/clear-all-cache'
    })
  },

  // 获取用户答题记录列表
  getUserResponseRecords: async (params: any): Promise<any> => {
    return await request.get({
      url: '/system/survey/statistics/user-response-records',
      params
    })
  },

  // 获取答题记录详情
  getResponseDetail: async (responseId: number): Promise<any> => {
    return await request.get({
      url: '/system/survey/statistics/response-detail',
      params: { responseId }
    })
  }

}
