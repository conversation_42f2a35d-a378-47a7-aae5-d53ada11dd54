import request from '@/config/axios'
import type {
  SurveyResponse,
  ResponseQuery,
  ResponseListQuery,
  PageResult
} from '../types'

export const ResponseApi = {
  // 获取回答分页列表
  getPage: async (params: ResponseQuery): Promise<PageResult<SurveyResponse>> => {
    return await request.get({
      url: '/system/survey/response/page',
      params
    })
  },

  // 获取回答列表
  getList: async (params?: ResponseListQuery): Promise<SurveyResponse[]> => {
    return await request.get({
      url: '/system/survey/response/list',
      params
    })
  },

  // 获取回答详情
  get: async (id: number): Promise<SurveyResponse> => {
    return await request.get({
      url: '/system/survey/response/get',
      params: { id }
    })
  },

  // 删除回答
  delete: async (id: number): Promise<boolean> => {
    return await request.delete({
      url: '/system/survey/response/delete',
      params: { id }
    })
  },

  // 导出回答Excel
  exportExcel: async (params: ResponseQuery): Promise<void> => {
    return await request.download({
      url: '/system/survey/response/export-excel',
      params
    })
  }
}
