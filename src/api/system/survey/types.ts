// 问题类型枚举 (SurveyQuestionTypeEnum)
export enum QuestionTypeEnum {
  SINGLE_CHOICE = 1,    // 单选题
  MULTIPLE_CHOICE = 2,  // 多选题
  TRUE_FALSE = 3,       // 判断题
  RATING = 4,           // 评价题
  FILE_UPLOAD = 5,      // 文件上传题
  TEXT = 6              // 文本题
}

// 问卷实例状态枚举 (SurveyInstanceStatusEnum)
export enum InstanceStatusEnum {
  UNPUBLISHED = 0,  // 未发布
  ONGOING = 1,      // 进行中
  ENDED = 2,        // 已结束
  DRAFT = 3         // 草稿
}

// 作用域类型枚举 (SurveyScopeTypeEnum)
export enum ScopeTypeEnum {
  PUBLIC = 1,       // 公开问卷
  INTERNAL = 2      // 企业内部问卷
}

// 作用域目标类型枚举 (SurveyScopeTargetTypeEnum)
export enum ScopeTargetTypeEnum {
  USER = 1,         // 用户
  DEPT = 2,         // 部门
  ROLE = 3         // 角色
}

// 提交频率枚举 (SurveySubmissionFrequencyEnum)
export enum SubmissionFrequencyEnum {
  ONCE = 1,         // 单次提交
  MULTIPLE = 2,     // 多次提交
  UNLIMITED = 3     // 无限制提交
}

// 通用状态枚举 (CommonStatusEnum) - 用于分类和模板
export enum CommonStatusEnum {
  ENABLED = 0,      // 开启/启用
  DISABLED = 1      // 关闭/禁用
}

// 回答状态枚举 (SurveyResponseStatusEnum)
export enum ResponseStatusEnum {
  SUBMITTED = 1,    // 已提交
  DRAFT = 2         // 草稿
}

// 选项配置接口
export interface Option {
  value: string
  text: string
  score?: number
  isOther?: boolean
}

// 问题配置接口
export interface QuestionConfig {
  // 通用配置
  placeholder?: string
  helpText?: string

  // 选择题配置
  options?: Option[]
  allowOther?: boolean
  otherText?: string

  // 多选题特有配置
  minSelections?: number
  maxSelections?: number

  // 评分题配置
  minRating?: number
  maxRating?: number
  ratingLabels?: string[]
  ratingType?: 'star' | 'number' | 'emoji'

  // 文件上传配置
  fileTypes?: string[]
  maxFileSize?: number
  maxFileCount?: number

  // 文本题配置
  inputType?: 'text' | 'textarea' | 'number' | 'email' | 'phone'
  maxLength?: number
  minLength?: number
  pattern?: string

  // 判断题配置
  trueText?: string
  falseText?: string
  correctAnswer?: boolean

  // 分数配置
  enableScoring?: boolean
  defaultScore?: number
}

// 问卷分类接口
export interface SurveyCategory {
  id: number
  name: string
  parentId: number
  sort: number
  status: CommonStatusEnum
  description?: string
  createTime: string
  children?: SurveyCategory[]
}

// 问卷分类保存请求接口
export interface SurveyCategorySaveReq {
  id?: number
  parentId: number
  name: string
  description?: string
  sort?: number
  status: CommonStatusEnum
}

// 问卷模板接口
export interface SurveyTemplate {
  id: number
  name: string
  templateCode: string
  description?: string
  categoryId?: number
  categoryName?: string
  status: CommonStatusEnum
  questionCount: number
  config?: any
  createTime: string
  creator?: string
  questions?: SurveyQuestion[]
}

// 问卷模板保存请求接口
export interface SurveyTemplateSaveReq {
  id?: number
  templateCode?: string
  name: string
  categoryId?: number
  description?: string
  status: CommonStatusEnum
  config?: any
  questions?: SurveyQuestionSaveReq[]
}

// 问卷问题接口
export interface SurveyQuestion {
  id: number
  templateId: number
  templateName?: string
  questionType: QuestionTypeEnum
  questionTypeName?: string
  title: string
  description?: string
  required: boolean
  sort: number
  config?: any
  createTime: string
  creator?: string
}

// 问卷问题保存请求接口
export interface SurveyQuestionSaveReq {
  id?: number
  templateId: number
  questionType: QuestionTypeEnum
  title: string
  description?: string
  required: boolean
  sort?: number
  config?: any
}

// 问卷实例接口
export interface SurveyInstance {
  id: number
  name: string
  templateId: number
  templateName?: string
  description?: string
  deptId: number | null
  deptName?: string
  startTime?: string
  endTime?: string
  status: InstanceStatusEnum
  statusName?: string
  responseCount?: number
  maxResponses?: number
  scopeType: ScopeTypeEnum
  scopeTypeName?: string
  anonymousEnabled?: boolean
  submissionFrequency: SubmissionFrequencyEnum
  submissionFrequencyName?: string
  maxSubmissions?: number
  submissionInterval?: number
  allowViewStatistics?: boolean
  config?: any
  createTime: string
  creator?: string
  scopes?: SurveyInstanceScope[]
}

// 问卷实例保存请求接口
export interface SurveyInstanceSaveReq {
  id?: number
  templateId: number
  deptId: number
  name: string
  description?: string
  startTime?: string
  endTime?: string
  status: InstanceStatusEnum
  maxResponses?: number
  scopeType: ScopeTypeEnum
  scopes?: SurveyInstanceScopeReq[]
  anonymousEnabled?: boolean
  submissionFrequency: SubmissionFrequencyEnum
  maxSubmissions?: number
  submissionInterval?: number
  allowViewStatistics?: boolean
  config?: any
}

// 问卷实例作用域接口
export interface SurveyInstanceScope {
  id: number
  instanceId: number
  scopeType: ScopeTypeEnum
  targetType: ScopeTargetTypeEnum
  targetTypeName?: string
  targetId: number
  targetName?: string
}

// 问卷实例作用域请求接口
export interface SurveyInstanceScopeReq {
  targetType: ScopeTargetTypeEnum
  targetId: number
}

// 问卷回答接口
export interface SurveyResponse {
  id: number
  instanceId: number
  instanceName?: string
  userId: number
  userName?: string
  submitTime?: string
  completionTime?: number
  status: ResponseStatusEnum
  statusName?: string
  score?: number
  ipAddress?: string
  userAgent?: string
  createTime: string
  answers?: SurveyResponseAnswer[]
}

// 问卷回答答案接口
export interface SurveyResponseAnswer {
  id: number
  responseId: number
  questionId: number
  questionTitle?: string
  questionType?: QuestionTypeEnum
  questionTypeName?: string
  answerText?: string
  answerValue?: string
  fileUrl?: string
  score?: number
}

// 统计数据接口
export interface SurveyStatistics {
  instanceId: number
  instanceName: string
  statisticsTime: string
  totalParticipants: number
  validResponses: number
  participationRate: number
  averageScore: number
  maxScore: number
  minScore: number
  averageCompletionTime: number
  questionStatistics: SurveyQuestionStatistics[]
  timeDistribution: SurveyTimeDistribution[]
  deptDistribution: SurveyDeptDistribution[]
}

// 问题统计接口
export interface SurveyQuestionStatistics {
  questionId: number
  questionTitle: string
  questionType: QuestionTypeEnum
  questionTypeName: string
  required: boolean
  responseCount: number
  skipCount: number
  responseRate: number
  averageScore: number
  optionStatistics: SurveyOptionStatistics[]
  textAnswers: string[]
}

// 选项统计接口
export interface SurveyOptionStatistics {
  optionValue: string
  optionText: string
  count: number
  percentage: number
  score: number
}

// 时间分布统计接口
export interface SurveyTimeDistribution {
  timePeriod: string
  count: number
  cumulativeCount: number
}

// 部门分布统计接口
export interface SurveyDeptDistribution {
  deptId: number
  deptName: string
  participantCount: number
  totalCount: number
  participationRate: number
  averageScore: number
}

// 分页查询参数接口
export interface PageQuery {
  pageNo: number
  pageSize: number
}

// 分页结果接口
export interface PageResult<T> {
  list: T[]
  total: number
}

// 分类查询参数
export interface CategoryQuery extends PageQuery {
  name?: string
  parentId?: number
  status?: CommonStatusEnum
  createTime?: string
}

// 分类列表查询参数（不分页）
export interface CategoryListQuery {
  name?: string
  parentId?: number
  status?: CommonStatusEnum
}

// 模板查询参数
export interface TemplateQuery extends PageQuery {
  name?: string
  templateCode?: string
  categoryId?: number
  status?: CommonStatusEnum
  createTime?: string
}

// 模板列表查询参数（不分页）
export interface TemplateListQuery {
  name?: string
  categoryId?: number
  status?: CommonStatusEnum
}

// 问题查询参数
export interface QuestionQuery extends PageQuery {
  templateId?: number
  questionType?: QuestionTypeEnum
  title?: string
  required?: boolean
  createTime?: string
}

// 问题列表查询参数（不分页）
export interface QuestionListQuery {
  templateId?: number
  questionType?: QuestionTypeEnum
  title?: string
  required?: boolean
}

// 实例查询参数
export interface InstanceQuery extends PageQuery {
  name?: string
  templateId?: number
  deptId?: number
  status?: InstanceStatusEnum
  scopeType?: ScopeTypeEnum
  anonymousEnabled?: boolean
  submissionFrequency?: SubmissionFrequencyEnum
  startTime?: string
  endTime?: string
  createTime?: string
}

// 实例列表查询参数（不分页）
export interface InstanceListQuery {
  name?: string
  templateId?: number
  deptId?: number
  status?: InstanceStatusEnum
  scopeType?: ScopeTypeEnum
  anonymousEnabled?: boolean
  submissionFrequency?: SubmissionFrequencyEnum
}

// 回答查询参数
export interface ResponseQuery extends PageQuery {
  instanceId?: number
  userId?: number
  userName?: string
  status?: ResponseStatusEnum
  submitTime?: string
  createTime?: string
}

// 回答列表查询参数（不分页）
export interface ResponseListQuery {
  instanceId?: number
  userId?: number
  userName?: string
  status?: ResponseStatusEnum
}

// 统计查询参数
export interface StatisticsQuery {
  instanceId: number
  startTime?: string
  endTime?: string
  onlyLatest?: boolean
  deptId?: number
  userId?: number
  timeType?: string  // 时间分布类型：day/week/month
}

// 分页查询基础接口
export interface PageQuery {
  pageNo?: number
  pageSize?: number
}

// 分页结果接口
export interface PageResult<T> {
  list: T[]
  total: number
}

// 用户信息接口（用于权限验证）
export interface UserInfo {
  id: number
  username: string
  nickname: string
  email?: string
  phone?: string
  deptId: number
  roleIds: number[]
}

// 权限验证结果接口
export interface PermissionResult {
  hasPermission: boolean
  reason?: string
}

// 提交验证结果接口
export interface SubmissionValidation {
  canSubmit: boolean
  message?: string
}

// 文件信息接口
export interface FileInfo {
  id: string
  name: string
  size: number
  type: string
  url: string
}
