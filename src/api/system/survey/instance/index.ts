import request from '@/config/axios'
import type {
  SurveyInstance,
  SurveyInstanceSaveReq,
  InstanceQuery,
  InstanceListQuery,
  PageResult
} from '../types'

export const InstanceApi = {
  // 获取实例分页列表
  getPage: async (params: InstanceQuery): Promise<PageResult<SurveyInstance>> => {
    return await request.get({
      url: '/system/survey/instance/page',
      params
    })
  },

  // 获取实例列表
  getList: async (params?: InstanceListQuery): Promise<SurveyInstance[]> => {
    return await request.get({
      url: '/system/survey/instance/list',
      params
    })
  },

  // 获取实例详情
  get: async (id: number): Promise<SurveyInstance> => {
    return await request.get({
      url: '/system/survey/instance/get',
      params: { id }
    })
  },

  // 创建实例
  create: async (data: SurveyInstanceSaveReq): Promise<number> => {
    return await request.post({
      url: '/system/survey/instance/create',
      data
    })
  },

  // 更新实例
  update: async (data: SurveyInstanceSaveReq): Promise<boolean> => {
    return await request.put({
      url: '/system/survey/instance/update',
      data
    })
  },

  // 删除实例
  delete: async (id: number): Promise<boolean> => {
    return await request.delete({
      url: '/system/survey/instance/delete',
      params: { id }
    })
  },

  // 发布实例
  publish: async (id: number): Promise<boolean> => {
    return await request.post({
      url: '/system/survey/instance/publish',
      params: { id }
    })
  },

  // 结束实例
  end: async (id: number): Promise<boolean> => {
    return await request.post({
      url: '/system/survey/instance/end',
      params: { id }
    })
  },

  // 检查用户是否可以参与问卷
  checkParticipate: async (instanceId: number, userId: number): Promise<boolean> => {
    return await request.get({
      url: '/system/survey/instance/check-participate',
      params: { instanceId, userId }
    })
  },

  // 检查用户是否可以提交问卷
  checkSubmit: async (instanceId: number, userId: number): Promise<boolean> => {
    return await request.get({
      url: '/system/survey/instance/check-submit',
      params: { instanceId, userId }
    })
  },


  // 导出实例Excel
  exportExcel: async (params: InstanceQuery): Promise<void> => {
    return await request.download({
      url: '/system/survey/instance/export-excel',
      params
    })
  }
}
