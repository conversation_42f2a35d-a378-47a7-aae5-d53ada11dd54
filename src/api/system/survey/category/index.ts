import request from '@/config/axios'
import type {
  SurveyCategory,
  SurveyCategorySaveReq,
  CategoryQuery,
  CategoryListQuery,
  PageQuery,
  PageResult
} from '../types'

export const CategoryApi = {
  // 获取子分类列表
  getChildren: async (parentId: number): Promise<SurveyCategory[]> => {
    return await request.get({
      url: '/system/survey/category/children',
      params: { parentId }
    })
  },

  // 获取分类树
  getTree: async (params?: CategoryListQuery): Promise<SurveyCategory[]> => {
    return await request.get({
      url: '/system/survey/category/tree',
      params
    })
  },

  // 获取分类列表
  getList: async (params?: CategoryListQuery): Promise<SurveyCategory[]> => {
    return await request.get({
      url: '/system/survey/category/list',
      params
    })
  },

  // 获取分类分页
  getPage: async (params: CategoryQuery): Promise<PageResult<SurveyCategory>> => {
    return await request.get({
      url: '/system/survey/category/page',
      params
    })
  },

  // 获取分类详情
  get: async (id: number): Promise<SurveyCategory> => {
    return await request.get({
      url: '/system/survey/category/get',
      params: { id }
    })
  },

  // 创建分类
  create: async (data: SurveyCategorySaveReq): Promise<number> => {
    return await request.post({
      url: '/system/survey/category/create',
      data
    })
  },

  // 更新分类
  update: async (data: SurveyCategorySaveReq): Promise<boolean> => {
    return await request.put({
      url: '/system/survey/category/update',
      data
    })
  },

  // 删除分类
  delete: async (id: number): Promise<boolean> => {
    return await request.delete({
      url: '/system/survey/category/delete',
      params: { id }
    })
  },

  // 导出分类Excel
  exportExcel: async (params: CategoryQuery): Promise<void> => {
    return await request.download({
      url: '/system/survey/category/export-excel',
      params
    })
  }
}
