import request from '@/config/axios'
import type {
  SurveyTemplate,
  SurveyTemplateSaveReq,
  TemplateQuery,
  TemplateListQuery,
  PageResult
} from '../types'

export const TemplateApi = {
  // 获取模板分页列表
  getPage: async (params: TemplateQuery): Promise<PageResult<SurveyTemplate>> => {
    return await request.get({
      url: '/system/survey/template/page',
      params
    })
  },

  // 获取模板列表
  getList: async (params?: TemplateListQuery): Promise<SurveyTemplate[]> => {
    return await request.get({
      url: '/system/survey/template/list',
      params
    })
  },

  // 获取模板详情
  get: async (id: number): Promise<SurveyTemplate> => {
    return await request.get({
      url: '/system/survey/template/get',
      params: { id }
    })
  },

  // 根据模板标识码获取模板详情
  getByTemplateCode: async (templateCode: string): Promise<SurveyTemplate> => {
    return await request.get({
      url: '/system/survey/template/get-by-template-code',
      params: { templateCode }
    })
  },

  // 创建模板
  create: async (data: SurveyTemplateSaveReq): Promise<number> => {
    return await request.post({
      url: '/system/survey/template/create',
      data
    })
  },

  // 更新模板
  update: async (data: SurveyTemplateSaveReq): Promise<boolean> => {
    return await request.put({
      url: '/system/survey/template/update',
      data
    })
  },

  // 删除模板
  delete: async (id: number): Promise<boolean> => {
    return await request.delete({
      url: '/system/survey/template/delete',
      params: { id }
    })
  },

  // 生成模板标识码
  generateTemplateCode: async (): Promise<string> => {
    return await request.post({
      url: '/system/survey/template/generate-template-code'
    })
  },

  // 导出模板Excel
  exportExcel: async (params: TemplateQuery): Promise<void> => {
    return await request.download({
      url: '/system/survey/template/export-excel',
      params
    })
  }
}
