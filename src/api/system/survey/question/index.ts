import request from '@/config/axios'
import type {
  SurveyQuestion,
  SurveyQuestionSaveReq,
  QuestionQuery,
  QuestionListQuery,
  PageResult
} from '../types'

export const QuestionApi = {
  // 获取问题分页列表
  getPage: async (params: QuestionQuery): Promise<PageResult<SurveyQuestion>> => {
    return await request.get({
      url: '/system/survey/question/page',
      params
    })
  },

  // 获取问题列表
  getList: async (params?: QuestionListQuery): Promise<SurveyQuestion[]> => {
    return await request.get({
      url: '/system/survey/question/list',
      params
    })
  },

  // 根据模板ID获取问题列表
  getByTemplate: async (templateId: number): Promise<SurveyQuestion[]> => {
    return await request.get({
      url: '/system/survey/question/list-by-template',
      params: { templateId }
    })
  },

  // 获取问题详情
  get: async (id: number): Promise<SurveyQuestion> => {
    return await request.get({
      url: '/system/survey/question/get',
      params: { id }
    })
  },

  // 创建问题
  create: async (data: SurveyQuestionSaveReq): Promise<number> => {
    return await request.post({
      url: '/system/survey/question/create',
      data
    })
  },

  // 批量创建问题
  batchCreate: async (templateId: number, data: SurveyQuestionSaveReq[]): Promise<boolean> => {
    return await request.post({
      url: '/system/survey/question/batch-create',
      params: { templateId },
      data
    })
  },

  // 更新问题
  update: async (data: SurveyQuestionSaveReq): Promise<boolean> => {
    return await request.put({
      url: '/system/survey/question/update',
      data
    })
  },

  // 删除问题
  delete: async (id: number): Promise<boolean> => {
    return await request.delete({
      url: '/system/survey/question/delete',
      params: { id }
    })
  },

  // 更新问题排序
  updateSort: async (id: number, sort: number): Promise<boolean> => {
    return await request.put({
      url: '/system/survey/question/update-sort',
      params: { id, sort }
    })
  },

  // 导出问题Excel
  exportExcel: async (params: QuestionQuery): Promise<void> => {
    return await request.download({
      url: '/system/survey/question/export-excel',
      params
    })
  }
}
