import request from '@/config/axios'
export interface ConfigReqVO {
  pageNum: number
  pageSize: number
  configName: string
  configKey: string
  configType: string
  dateRange: Date
}

export interface ConfigRespVO {
  configId?: number
  configKey: string
  configName: string
  configType: string
  configValue: string
  remark: string
  createBy: string
}

export interface ConfigSaveVO extends ConfigRespVO{}



// 此为一期的API配置文件,后面统一用二期的config配置API,此文件不用改，目前用不到  但是不要删除该文件 ！！！
// 查询参数列表
export const listConfig = (params: ConfigReqVO) => {
  return request.get({ url: '/adapter/v1/config', params })
}

// 查询参数详细
export const getConfig = (configId: number) => {
  return request.get({ url: `/adapter/v1/config/id/${configId}` })
}

// 根据参数键名查询参数值
export const getConfigKey = (configKey: string) => {
  return request.get({ url: `/adapter/v1/config/key/${configKey}` })
}

// 新增参数配置
export const addConfig = (data: ConfigSaveVO) => {
  return request.post({ url: '/adapter/v1/config', data})
}

// 修改参数配置
export const updateConfig = (data: ConfigSaveVO) => {
  return request.put({ url: '/adapter/v1/config', data})
}

// 删除参数配置
export const delConfig = (configId: number) => {
  return request.delete({ url: `/adapter/v1/config/${configId}`})
}

// 刷新参数缓存
export const refreshCache = () => {
  return request.delete({ url: '/adapter/v1/config/refreshCache' })
}

// 导出参数配置
export const exportConfig = (params: any) => {
  return request.download({ url: '/adapter/v1/config/export', params })
}
