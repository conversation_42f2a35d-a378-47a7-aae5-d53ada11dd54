import request from '@/config/axios'

export interface CompanyReqVO {
  deptName: string
  status: string
  type: number
  serviceCompanyId: number
  deptCode: string
  dataSource: string
}
export interface CompanyRespVO {
  id: number
  name: string
  companyId: number
  deptName: string
  shortName: string
  type: number
  deptCode: string
  serviceCompanyId: number
  orderNum: number
  status: string
  createTime: Date
  dataSource: string
  createBy: string
  deptId: number
}
export interface CompanySaveVO {
  companyId?: number
  deptCode: string
  deptName: string
  orderNum: number
  shortName: string
  status: string
  type: number
}
// 公司供应商管理员 VO
export interface UserContractHolderSaveVO {
  id: number // 主键ID
  userId: number // 用户ID
  companyId: number // 公司id
}
export interface UserContractHolderReqVO {
  pageNo: number
  pageSize: number
  companyId: number // 公司id
}

export interface UserContractHolderRespVO {
  id: number
  userId: number
  companyId: number
  createTime: Date
}

// 公司类型枚举
export enum CompanyTypeEnum {
  SERVICE = 0, // 服务公司
  SUPPLIER = 1, // 供应商
}
// 公司状态枚举
export enum CompanyStatusEnum {
  ENABLE = 0, // 启用
  DISABLE = 1, // 禁用
}
// 查询公司列表
export const listCompany = (params?: CompanyReqVO) => {
  return request.get<IResponse<CompanyRespVO[]>>({ url: '/system/company', params })
}

// 查询公司列表（排除节点）
export const listCompanyExcludeChild = (deptId: number) => {
  return request.get({ url: `/system/company/exclude/${deptId}`})
}

// 查询公司详细
export const getCompany = (id: number) => {
  return request.get<IResponse<CompanyRespVO>>({ url: `/system/company/${id}`})
}

// 新增公司
export const addCompany = (data: CompanySaveVO) => {
  return request.post({ url: '/system/company', data})
}

// 修改公司
export const updateCompany = (data: CompanySaveVO) => {
  return request.put({ url: '/system/company', data})
}

// 删除公司
export const delCompany = (deptId: number) => {
  return request.delete({ url: `/system/company/${deptId}`})
}

// 查询公司供应商管理员分页
export const getUserContractHolderPage = (params: UserContractHolderReqVO) => {
  return request.get<PageResult<UserContractHolderRespVO[]>>({ url: `/system/user-contract-holder/page`, params })
}
// 查询公司供应商管理员详情
export const getUserContractHolder = (id: number) => {
  return request.get<IResponse<UserContractHolderRespVO>>({ url: `/system/user-contract-holder/get?id=` + id })
}

// 新增公司供应商管理员
export const createUserContractHolder = (data: UserContractHolderSaveVO) => {
  return request.post({ url: `/system/user-contract-holder/create`, data })
}

