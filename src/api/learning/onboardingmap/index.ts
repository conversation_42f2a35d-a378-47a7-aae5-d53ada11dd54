import request from '@/config/axios'
import {ContentTypeEnum} from "@/views/learning-center/onboardingmap/components/type";

// 学习任务
export interface OnboardingMapTask {
  id?: number
  nodeId: number
  bizType: ContentTypeEnum
  bizId: string
  bizName: string
  duration: number
  mandatory: boolean
}

// 节点
export interface OnboardingMapNode {
  id?: number
  mapId: number
  name: string
  icon: string
  sort: number
  tasks: OnboardingMapTask[]
}

// 学习地图 VO
export interface OnboardingMapVO {
  id: number // 主键id
  name: string // 学习地图名称
  mode: number // 学习模式 10.自由学习 20. 阶段性学习
  effectiveVersion: number // 当前生效的版本号
  // meta: string;
  createTime: string
  nodes: OnboardingMapNode[]
}



// 学习地图 API
export const OnboardingMapApi = {
  // 查询学习地图分页
  getOnboardingMapPage: async (params: any) => {
    return await request.get({ url: `/learning/onboarding-map/page`, params })
  },

  // 查询学习地图详情
  getOnboardingMap: async (id: number) => {
    return await request.get({ url: `/learning/onboarding-map/get?id=` + id })
  },

  // 新增学习地图
  createOnboardingMap: async (data: OnboardingMapVO) => {
    return await request.post({ url: `/learning/onboarding-map/create`, data })
  },

  // 修改学习地图
  publishOnboardingMap: async (data: OnboardingMapVO) => {
    return await request.post({ url: `/learning/onboarding-map/publish`, data })
  },

  // 删除学习地图
  deleteOnboardingMap: async (id: number) => {
    return await request.delete({ url: `/learning/onboarding-map/delete?id=` + id })
  },

  // 导出学习地图 Excel
  exportOnboardingMap: async (params) => {
    return await request.download({ url: `/learning/onboarding-map/export-excel`, params })
  }
}
