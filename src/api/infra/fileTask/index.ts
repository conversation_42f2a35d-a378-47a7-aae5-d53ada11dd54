import request from '@/config/axios'

const code = import.meta.env.VITE_STORAGE_CODE

export interface UploadResult {
  url: string // 文件s3存储链接
  path: string // 文件存储路径，如：archive/6064/eb91bb25-1a87-48a9-8406-422db580a122.png
  fileId: number // 文件id
  folderId: number // 文件夹id
}

export interface FileTaskDO {
  /**
   * ID
   */
  id: number
  /**
   * 分片上传的uploadId
   */
  uploadId: string
  /**
   * 文件唯一标识（md5）
   */
  identifier: string
  /**
   * 文件名
   */
  fileName: string
  /**
   * 所属桶名
   */
  bucketName: string
  /**
   * 文件的key（路径）
   */
  objectKey: string
  /**
   * 文件大小（byte）
   */
  totalSize: number
  /**
   * 每个分片大小（byte）
   */
  chunkSize: number
  /**
   * 分片数量
   */
  chunkNum: number
}

export interface Part {
  partNumber: number
  eTag: string
  lastModified: number
  size: number
}

export interface TaskRecordDTO extends FileTaskDO {
  exitPartList: Part[]
}

export interface TaskInfoDTO {
  finished: boolean
  url: string
  path: string
  taskRecord: TaskRecordDTO
  fileId: number
  folderId: number
}

/**
 * 分片
 */
export interface FileTaskInitReqVO {
  identifier: string // 文件md5
  fileName: string // 文件名称
  type: string // 文件类型
  code?: string // 存储器配置编码 s3_xxx
  totalSize: number // 文件大小
  chunkSize: number // 分块大小
  // fileType: number // 文件类型 1-folder, 2-file
  currentFolderId: number // 当前目录id 位于根目录请传1
  relativePath: string // 上传的相对文件路径
}

export interface FileTaskMergeReqVO {
  code?: string // 存储器配置编码 s3_xxx
  identifier: string // 文件md5
  fileType: string // 文件类型
  digitFlag: boolean // 是否需要数字化
  currentFolderId: number // 当前文件夹id
  relativePath: string // 文件路径
}

/**
 * 根据文件的md5获取未上传完的任务
 * @param identifier 文件md5
 * @returns {Promise<AxiosResponse<any>>}
 */
export const getFileTask = async (
  identifier: string,
  folderId: number,
  relativePath: string,
  name: string,
  type
) => {
  return await request.get({
    url: `/infra/file/task`,
    params: { code, identifier, folderId, relativePath, name, type }
  })
}

/**
 * 初始化一个分片上传任务
 * @returns {Promise<AxiosResponse<any>>}
 * @param taskReqVO
 */
// export const initFileTask = async (taskReqVO: FileTaskInitReqVO) => {
//   taskReqVO.code = code
//   return await request.post({ url: '/infra/file/task/init', data: taskReqVO })
// }

export const initFileTask = async (taskReqVO: FileTaskInitReqVO) => {
  taskReqVO.code = code
  return await request.post({ url: '/infra/file/v2/task/init', data: taskReqVO })
}

/**
 * 获取预签名分片上传地址
 * @returns {Promise<AxiosResponse<any>>}
 * @param identifier 文件唯一标识（md5）
 * @param partNumber 分片序号
 */
export const getFileTaskPreSignUrl = async (identifier: string, partNumber: number) => {
  return await request.get({
    url: '/infra/file/task/pre-sign-url',
    params: { code, identifier, partNumber }
  })
}

/**
 * 合并分片
 * @returns {Promise<AxiosResponse<any>>}
 * @param taskMergeReqVO 分片合并请求参数
 */
export const mergeFileTask = async (taskMergeReqVO: FileTaskMergeReqVO) => {
  taskMergeReqVO.code = code
  return await request.post({ url: `/infra/file/v2/task/merge`, data: taskMergeReqVO })
}
