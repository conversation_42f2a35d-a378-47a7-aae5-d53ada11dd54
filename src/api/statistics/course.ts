import request from '@/config/axios'
import { CourseReqVO } from "@/api/topicMgt/elearning"

export interface CourseInfoReqVO {
  id?: number
  email: string
  pageNo: number
  pageSize: number
  status: number
  studentName: string
  type: string
}
export interface CourseInfoRespVO {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  assignmentDetailId: number
  badgeNo?: string | null
  company?: string | null
  department?: string | null
  email?: string | null
  messageStatus?: string | null
  onSchedule: boolean
  operator?: string | null
  position?: string | null
  section?: string | null
  star?: string | null
  status?: number | null
  studentName?: string | null
  type?: string | null
  userId?: number | null
  examStatus: string | null
  examTimes: number | null
}

export interface CourseTableRespVO {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  introduction: string
  departmentId: string
  title: string
  ack: boolean
}

// 主表-统计
export const countTableData = (params: any) => {
  return request.get({ url: '/learning/course/statics/all', params })
}
/** 折现图 */
export const chartList = () => {
  return request.get({ url: '/learning/course-topic/topic/coursecount' })
}
/** 主表查询 */
export const tableList = (params: CourseReqVO) => {
  return request.get<PageResult<CourseTableRespVO[]>>({ url: '/learning/course/page', params })
}
/** 根据id查询course detail 的统计 */
export const detailCountData = (params: { id: number }) => {
  return request.get({ url: '/learning/course/statics', params })
}
/** Detail查询 */
export const detailList = (params: CourseInfoReqVO) => {
  return request.get<CourseInfoRespVO[]>({ url: '/learning/course/info', params })
}
/** course 导出*/
export const exportCourse = (params: any) => {
  return request.download({ url: '/learning/course/export', params })
}
