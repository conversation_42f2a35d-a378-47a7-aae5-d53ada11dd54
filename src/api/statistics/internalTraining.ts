import request from '@/config/axios'

export interface InternalTrainingReq {
  pageNo: number
  pageSize: number
  code: string
  title: string
  titleAr: string
  type: number
  place: string
  implementingCompany: string // 培训公司
  startTime: string
  endTime: string
  duration: number
  remark: string
}

export interface InternalTrainingRespVO {
  id?: number
  code: string
  title: string
  titleAr: string
  type: number
  place: string
  startTime: Date
  endTime: Date
  duration: number
  implementingCompany: string
  attachments: string
  remark: string
  createTime: Date
  userCount: number
}


export const InternalTrainingApi = {
  // 获得国内培训分页--my center
  getInternalPage: async (params: InternalTrainingReq) => {
    return await request.get({ url: `/academy/internal/my-training`, params })
  },
  // 导出国内培训分页--my center Excel
  exportInternal: async (params: InternalTrainingReq) => {
    return await request.download({ url: `/academy/internal/my-training/export-excel`, params })
  },
}
