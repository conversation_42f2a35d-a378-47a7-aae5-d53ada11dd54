import request from '@/config/axios'

export interface CourseDetailReqVo {
  pageNo: number
  pageSize: number
  courseTitle: string
  categoryId: number
  startDate: string
  endDate: string
}
export interface SignInAndPassReqVO {
  pageNo: number
  pageSize: number
  categoryId: number
  startDate: string
  endDate: string
}
export const MlcTrainingApi = {
  // 统计信息-Total Courses 课程总数
  getCourseTotal: async () => {
    return await request.get({ url: `/academy/statistics/course/total` })
  },
  // 统计信息-Offline Classes
  getClassTotal: async () => {
    return await request.get({ url: `/academy/statistics/class/total` })
  },
  // 统计信息-studentTotal 学生总数
  getStudentTotal: async () => {
    return await request.get({ url: `/academy/statistics/student/total` })
  },
  // 统计信息-课程详情 列表
  getCoursePage: async (params: CourseDetailReqVo) => {
    return await request.get({ url: `/academy/statistics/courseDetails`, params })
  },
  // HSE 统计信息-签到统计与通过统计(图1和图2 公用一个接口)
  getSignInAndPass: async (params: SignInAndPassReqVO) => {
    return await request.get({ url: `/academy/statistics/checkIn/statistics`, params })
  },
  // HSE 统计信息-公司未签到统计(图3)
  getNoSignIn: async (params: SignInAndPassReqVO) => {
    return await request.get({ url: `/academy/statistics/company/not-checkIn/total`, params })
  },
  // HSE 统计信息-公司签到统计(图4)
  getCompanySignIn: async (params: SignInAndPassReqVO) => {
    return await request.get({ url: `/academy/statistics/company/all-checkIn/total`, params })
  },
  // HSE 统计信息-课程签到统计(图5)
  getCourseSignIn: async (params: SignInAndPassReqVO) => {
    return await request.get({ url: `/academy/statistics/course/all-checkIn/total`, params })
  },
  // HSE 统计信息-课程包含课堂数量统计(图6)
  getCourseClass: async (params: SignInAndPassReqVO) => {
    return await request.get({ url: `/academy/statistics/course/class/total`, params })
  },
  // DDT 统计信息-课程签到统计(图一)
  // getCourseSignIn: async (params: SignInAndPassReqVO) => {
  //   return await request.get({ url: `/academy/statistics/course/all-checkIn/total`, params })
  // },
  // DDT 统计信息-公司未签到统计(图二)
  // getNoSignIn: async (params: SignInAndPassReqVO) => {
  //   return await request.get({ url: `/academy/statistics/company/not-checkIn/total`, params })
  // },
  // DDT 统计信息-公司签到统计(图三)
  // getCompanySignIn: async (params: SignInAndPassReqVO) => {
  //   return await request.get({ url: `/academy/statistics/company/all-checkIn/total`, params })
  // },
  // 导出统计信息-课程详情
  exportCourse: async (params: CourseDetailReqVo) => {
    return await request.download({ url: `/academy/statistics/courseDetails/export`, params })
  }
}
