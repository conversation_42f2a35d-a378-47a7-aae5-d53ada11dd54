import request from '@/config/axios'

export interface StatisticsClassReq {
  pageNo: number
  pageSize: number
  code: string
  courseName: string
  titleAr: string
  type: number
  place: string
  implementingCompany: string // 培训公司
  startTime: string
  endTime: string
  duration: number
  remark: string
}

export interface StatisticsClassResp {
  classId: number
  courseName: string
  validity: number
  categoryName: string
  name: string
  code: string
  type: number
  trainerId: number
  trainerName: string
  classRoomId: number
  classRoomName: string
  scanStartTime:Date
  checkInTime: Date
  checkOutTime: Date
  startTime: number
  endTime: number
  trainingDays: number
  language: number
  translator: number
  studyStatus: number
  status: number
  publishStatus: number
  maxNum: number
  minNum: number
  liveLink: string
  feedbackQrCode: string
  description: string
  createTime: Date
  assignNum: number
}
// 查询类型枚举 (1. 我的记录 2.通过课程id查询)
export enum QueryTypeEnum {
  MY_RECORDS = 1,
  THROUGH_COURSE_ID = 2
}

export const StatisticsClassApi = {
  // 获得课堂信息分页---my records(统计模块 student->Detail下的Mlc Training分页列表)
  getStatisticsClassPage: async (params: StatisticsClassReq) => {
    return await request.get({ url: `/academy/class-info/my-training`, params })
  },
  // 导出国内培训分页--my center Excel
  exportStatisticsClass: async (params: StatisticsClassReq) => {
    return await request.download({ url: `/academy/class-info/my-training/export-excel`, params })
  },
}
