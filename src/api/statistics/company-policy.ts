import request from '@/config/axios'

export interface CompanyPolicyReqVO {
  pageNo: number
  pageSize: number
  ack: boolean
  title: string
}
export interface CompanyPolicyRespVO {
  id: number
  title: string
  ackType: number
  ackTypeName: string
  status: number
  statusName: string
  assignedCount: number
  ackCount: number
  ackRate: number
  unAckCount: number
  createTime: Date
  creator: string
  companyName: string
  deptName: string
}

export interface CompanyPolicyRecordReqVO extends CompanyPolicyReqVO{
  companyPolicyId: number
  companyPolicyScopeId: number
  scopes: number
  userId: number
  status: number
  createTime: Date
  userName: string
  badgeNo: string
  email: string
}
export interface CompanyPolicyDetailReqVO {
  companyPolicyId: number
  email: string
  pageNo: number
  pageSize: number
  status: number
  userId: number
  userName: string
}

export interface CompanyPolicyRecordExportReqVO {
  companyName: string
  companyPolicyId: number
  createBy: string
  createTime: string
  createId: number
  email: string
  id: number
  duration: number
  pageNo: number
  pageSize: number
  positionName: string
  remark: string
  sectionName: string
  status: number
  userName: string
  userId: number
}
// 主表-统计
export const countCompanyPolicy = (params: any) => {
  return request.get({ url: '/learning/statistics/company/policy/count', params })
}
/** 主表查询 */
export const CompanyPolicyList = (params: CompanyPolicyReqVO) => {
  return request.get<PageResult<CompanyPolicyRespVO[]>>({ url: '/learning/statistics/company/policy/page', params })
}
/** 根据id查询company policy detail 的统计 */
export const detailCompanyPolicyCount = (companyPolicyId: number) => {
  return request.get({ url: `/learning/statistics/company/policy/detail/count/${companyPolicyId}` })
}
/** Detail查询 */
export const CompanyPolicyDetailList = (params: CompanyPolicyDetailReqVO) => {
  return request.get({ url: '/learning/statistics/company/policy/detail/page', params })
}
// 导出
export const exportCompanyPolicy = (params: CompanyPolicyRecordReqVO) => {
  return request.download({ url: '/learning/company-policy/record/export', params })
}
// 导出
export const exportCompanyPolicyDetail = (params: CompanyPolicyRecordExportReqVO) => {
  return request.download({ url: '/learning/statistics/company/policy/export', params })
}
