import request from '@/config/axios'

export interface LiveAggregateRepVO {
  startTime: string
  endTime: string
}
export interface LiveInfoPageReqVO {
  pageNo: number
  pageSize: number
  name: string
  status: number | string
  startTime: string
  endTime: string
}

export interface LiveInfoRespVO {
  roomId: number
  roomName: string
  speakers: {
    userId: number
    nickname: string
  }
  startTime: string
  endTime: string
  duration: string
  status: number
  studentNum: number
  attendanceStudentNum: string
  attendanceRate: number
}

export interface LiveDetailRespVO extends LiveInfoRespVO{
  students : studentInfoVO[]
}

export interface studentInfoVO {
  roomUserId: number
  userId: number
  nickname: string
  companyId: number
  companyName: string
  deptId: number
  deptName: string
  badgeNumber: string
  positionIds: number[]
  positionNames: string[]
  email: string
  joinType: number
  attendance: boolean
}


export const LiveStreamApi = {
  // 统计聚合数据
  getLiveInfoAggregate: async (params?: LiveAggregateRepVO) => {
    return await request.get({ url: '/live/statistics/aggregate', params })
  },

  // 统计日数据
  getLiveInfoDay: async (params: { startTime: string, endTime: string,top: number }) => {
    return await request.get({ url: '/live/statistics/daily', params })
  },

  // 直播列表分页
  getLiveInfoPage: async (params: LiveInfoPageReqVO) => {
    return await request.get({ url: '/live/statistics/page', params })
  },

  // 直播信息详情
  getLiveInfo: async (roomId: number) => {
    return await request.get({ url: '/live/statistics/detail?roomId=' + roomId })
  },

  // 导出直播信息
  exportLiveInfo: async (roomIds: number[]) => {
    return await request.download({ url: '/live/statistics/page/export?roomIds=' + roomIds })
  },

  // 导出直播的学生信息信息
  exportLiveUser: async (params: { roomId: number,roomUserIds: number[] }) => {
    return await request.download({ url: '/live/statistics/student/export', params })
  }
}
