import request from '@/config/axios'

export interface InternalReqVO {
  categoryId: number
  pageNo: number
  pageSize: number
  courseTitle: string
  startDate: string
  endDate: number
}

export const OutsourcedTrainingApi = {
  // =========== internalTraining ==============
  // 统计信息-Outsourced Courses total
  getOutsourcedCoursesTotal: async () => {
    return await request.get({ url: '/academy/statistics/outsourced/total' })
  },
  getStudentTotal: async () => {
    return await request.get({ url: '/academy/statistics/outsourced/studentTotal' })
  },
  // 统计信息-国内培训类型数量
  getInternalTypeTotal: async (params: InternalReqVO) => {
    return await request.get({ url: '/academy/statistics/internal/type/total', params })
  },
  // 统计信息-国内培训用户状态数量
  getInternalUserTotal: async (params: InternalReqVO) => {
    return await request.get({ url: '/academy/statistics/internal/user/status/total', params })
  },
  // 统计信息-国内培训公司用户数量
  getInternalCompanyTotal: async (params: InternalReqVO) => {
    return await request.get({ url: '/academy/statistics/internal/company/user/total', params })
  },
  // 统计信息-国内培训部门用户数量
  getInternalDeptTotal: async (params: InternalReqVO) => {
    return await request.get({ url: '/academy/statistics/internal/dept/user/total', params })
  },
  // =========== externalTraining ==============
  // 统计信息-国外付费公司用户数量
  getExternalCompanyStudentTotal: async (params: InternalReqVO) => {
    return await request.get({ url: '/academy/statistics/external/company/user/total', params })
  },
  // 统计信息-国外国家用户数量
  getExternalCountryStudentTotal: async (params: InternalReqVO) => {
    return await request.get({ url: '/academy/statistics/external/country/user/total', params })
  },
  // 统计信息-国外培训部门用户数量
  getExternalDeptStudentTotal: async (params: InternalReqVO) => {
    return await request.get({ url: '/academy/statistics/external/dept/user/total', params })
  }
}
