import request from '@/config/axios'

export interface OnboardingPageReqVO {
  pageNo: number
  pageSize: number
  title: string
  status: number
  categoryId: number
  deptIds: number[]
  companyId: number
}
export interface OnboardingDetailPageRespVO {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNum: number
  pageSize: number
  id: number
  introduction: string
  departmentId: string
  title: string
  ack: boolean
}
// 主表-统计
export const countTableData = (params: any) => {
  return request.get({ url: '/learning/statistics/onBoarding/count', params })
}
/** 主表查询 */
export const tableList = (params: OnboardingPageReqVO) => {
  return request.get({ url: '/learning/statistics/onBoarding/page', params })
}
/** 根据id查询onBoarding detail 的统计 */
export const detailCountData = (onBoardingId: number) => {
  return request.get({ url: `/learning/statistics/onBoarding/detail/count/${onBoardingId}` })
}
/** Detail查询 */
export const detailList = (params: OnboardingPageReqVO) => {
  return request.get<PageResult<OnboardingDetailPageRespVO[]>>({ url: '/learning/statistics/onBoarding/detail/page', params })
}
// 导出
export const exportData = (params: OnboardingPageReqVO) => {
  return request.download({ url: '/learning/statistics/onBoarding/page/export', params })
}

// 导出分配人信息
export const exportAssignData = (params: OnboardingPageReqVO) => {
  return request.download({ url: '/learning/onboarding/assignment/export', params })
}
