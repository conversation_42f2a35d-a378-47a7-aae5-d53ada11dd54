import request from '@/config/axios'

export interface InternalTrainingReq {
  pageNo: number
  pageSize: number
  code: string
  title: string
  titleAr: string
  receivingCountry: string
  travelDate: string
  returnDate: string
  adminNo: string
  costBearer: string
  remark: string
}

export interface InternalTrainingRespVO {
  id?: number
  code: string
  title: string
  titleAr: string
  receivingCountry: string
  travelDate: Date
  returnDate: Date
  costBearer: string
  adminNo: string
  remark: string
  createTime: Date
  userCount: number
}


export const ExternalTrainingApi = {
  // 获得国外培训分页--my center
  getExternalPage: async (params: InternalTrainingReq) => {
    return await request.get({ url: `/academy/external/my-training`, params })
  },
  // 导出国外培训分页--my center Excel
  exportExternal: async (params: InternalTrainingReq) => {
    return await request.download({ url: `/academy/external/my-training/export-excel`, params })
  },
}
