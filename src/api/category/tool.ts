// 时间转换为秒
export const switchSecond = (date) => {
  const time = new Date(date)
  const hours = time.getHours()
  const minutes = time.getMinutes()
  const seconds = time.getSeconds()
  const totalsecond = Number(hours * 3600) + Number(minutes * 60) + Number(seconds)
  return totalsecond
}
// 秒转化为时间
export const secondToTime = (seconds) => {
  const defaultTime = new Date(0, 0, 0, 0, 0, 0)
  defaultTime.setSeconds(seconds)
  return defaultTime
}

export const getDuration = (localURL) => {
  return new Promise((resolve) => {
    // let tempVideoEl
    // if (this.mediaType === 'video') {
    //   tempVideoEl = document.createElement('video');
    // } else {
    //   tempVideoEl = document.createElement('audio');
    // }
    const tempVideoEl = document.createElement('video')

    tempVideoEl.setAttribute('playsinline', 'true')
    tempVideoEl.setAttribute('webkit-playsinline', 'true')
    tempVideoEl.setAttribute('x5-playsinline', 'true')
    tempVideoEl.setAttribute('x5-video-player-type', 'h5')
    tempVideoEl.setAttribute('x5-video-player-fullscreen', 'false')

    tempVideoEl.src = localURL
    // 必须首先load()，否则移动端不能监听oncanplay
    tempVideoEl.load()
    // 必须设置currentTime，否则没有内容
    tempVideoEl.currentTime = 0.001
    tempVideoEl.oncanplay = () => {
      resolve(tempVideoEl.duration)
    }
  })
}
