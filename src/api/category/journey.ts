import request from '@/config/axios'


export interface JourneyCategoryReqVO {
  pageNo: number
  pageSize: number
  title: string
}
export interface JourneyCategoryRespVO {
  id: number
  createTime: Date
  title: string
  sort: number
}
export interface JourneyCategorySaveVO {
  id?: undefined,
  title: string
  sort: number
}

// 获取category列表
export const getJourneyCategory = (params: JourneyCategoryReqVO) => {
  return request.get<PageResult<JourneyCategoryRespVO[]>>({ url: '/learning/journey-category/page', params })
}
// 获取category列表(全量)
export const getJourneyCategoryAll = () => {
  return request.get({ url: '/learning/journey-category/simple-list' })
}

// 添加category
export const addJourneyCategory = (data: JourneyCategorySaveVO) => {
  return request.post({ url: '/learning/journey-category/create', data})
}
// 修改category
export const editJourneyCategory = (data: JourneyCategorySaveVO) => {
  return request.put({ url: '/learning/journey-category/update', data})
}
// 删除category
export const deleteJourneyCategory = (id: number) => {
  return request.delete({ url: `/learning/journey-category/delete?id=` + id })
}
