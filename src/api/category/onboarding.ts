import request from '@/config/axios'

export interface OnboardingReqVO extends OnboardingCategoryReqVO{
  categoryId: number
  departmentId: number
  type: string
  content: string
  keywords: string
  mediaType: string
  lang: number
  isMandatory: number
}

export interface OnboardingCategoryReqVO {
  pageNo: number
  pageSize: number
  title: string
}
export interface OnboardingCategoryRespVO {
  id: number
  createBy: string
  createTime: Date
  title: string
  sort: number
}
export interface OnboardingCategorySaveVO extends OnboardingCategoryReqVO{
  id?: number
  sort: number
}
// 获取onboarding列表
export const getOnboarding = (params: OnboardingReqVO) => {
  return request.get({ url: '/learning/onboarding', params })
}

// 删除onboarding
export const deleteOnboarding = (ids: string[]) => {
  return request.delete({ url: `/learning/onboarding/${ids}` })
}

// 获取category列表
export const getCategory = (params: OnboardingCategoryReqVO) => {
  return request.get<PageResult<OnboardingCategoryRespVO[]>>({ url: '/learning/onboarding/category', params })
}

// 添加category
export const addCategory = (data: OnboardingCategorySaveVO) => {
  return request.post({ url: '/learning/onboarding/category', data})
}

// 修改category
export const EditCategory = (data: OnboardingCategorySaveVO) => {
  return request.put({ url: '/learning/onboarding/category', data})
}
// 删除category
export const deleteCategory = (ids: string[]) => {
  return request.delete({ url: `/learning/onboarding/category/${ids}` })
}
// 查询onboarding列表没有分页
export const getOnboardingCategory = () => {
  return request.get({ url: '/learning/onboarding/category/simple-list' })
}
