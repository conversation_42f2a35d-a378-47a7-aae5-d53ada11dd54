import request from '@/config/axios'
import {IResponse} from "../../../types/global";

export interface TopicReqVO {
  pageNo: number
  pageSize: number
  name: string
}
export interface TopicRespVO {
  createTime: Date
  id: number
  parentId?: number
  ancestors: string
  name: string
  keywords?: string | null
  introduction: string
  sort?: number | null
  cover: string
}
export interface TopicSaveVO {
  name: string
  keywords: string
  sort: number
  introduction: string
  parentId?: number
  id?: number
  cover: string
}
// 查询专题列表
export const listTopic = (params: TopicReqVO) => {
  return request.get<IResponse<TopicRespVO[]>>({ url: '/learning/course-topic/topic', params })
}

// 新增专题
export const addTopic = (data: TopicSaveVO) => {
  return request.post({ url: '/learning/course-topic/topic', data})
}

// 修改专题课程
export const updateTopic = (data: TopicSaveVO) => {
  return request.put({ url: '/learning/course-topic/topic', data})
}

// 删除专题课程
export const delTopic = (id: number) => {
  return request.delete({ url: `/learning/course-topic/topic/${id}` })
}
// 查询专题列表无分页
export const listTopicAll = () => {
  return request.get<IResponse<TopicRespVO[]>>({ url: '/learning/course-topic/all' })
}
