import request from '@/config/axios'

export interface OrientationCategoryReqVO {
  pageNo: number
  pageSize: number
  title: string
}

export interface OrientationCategoryRespVO {
  id: number
  createBy: string
  createTime: Date
  title: string
  sort: number
}
export interface OrientationCategorySaveVO extends OrientationCategoryReqVO{
  id?: number
  sort: number
}

// 获取category列表
export const getCategory = (params: OrientationCategoryReqVO) => {
  return request.get({ url: '/learning/orientation/category', params })
}

// 添加category
export const addCategory = (data: OrientationCategorySaveVO) => {
  return request.post({ url: '/learning/orientation/category', data})
}

// 修改category
export const EditCategory = (data: OrientationCategorySaveVO) => {
  return request.put({ url: '/learning/orientation/category', data})
}
// 删除category
export const deleteCategory = (ids: string[]) => {
  return request.delete({ url: `/learning/orientation/category/${ids}` })
}
// 查询orientation列表没有分页
export const getOrientationCategory = () => {
  return request.get({ url: '/learning/orientation/category/simple-list' })
}
