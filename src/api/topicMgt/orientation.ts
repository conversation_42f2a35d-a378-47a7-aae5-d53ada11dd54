import request from '@/config/axios'


export interface OrientationReqVO {
  pageNo: number
  pageSize: number
  title: string
  departmentId: number
  categoryId: number
  display: boolean
  lang: string
  duration: number
  mediaType: number
}

export interface OrientationRespVO {
  categoryId: number
  categoryName: string
  content: string
  cover: string
  createBy: string
  createTime: string
  departmentId: number
  departmentName: string
  description: string
  display: boolean
  duration: number
  durationLower: number
  durationUpper: number
  format: string
  id: number
  keywords: string
  lang: string
  mediaType: number
  origin: number
  remark: string
  sort: number
  title: string
  updateBy: string
  updateId: number
  updateTime: string
}
export interface OrientationSaveVO extends OrientationReqVO {
  cover: string
  format: string
  id?: number
  keywords: string
  origin: number
  remark: string
  sort: number
}


export interface OrientationAssignReqVO {
  pageNo: number
  pageSize: number
  remark: string
  scope: number
  updateBy: string
  updateTime: string
}

export interface OrientationAssignRespVO {
  id: number
  orientationId: number
  scope: number
  relevanceId: number
  relevanceName: string
  createTime: string
}


// 获取orientation列表
export const getOrientation = (params: OrientationReqVO) => {
  return request.get<PageResult<OrientationRespVO[]>>({ url: '/learning/orientation', params })
}

// 删除orientation
export const deleteOrientation = (ids: number[]) => {
  return request.delete({ url: `/learning/orientation/${ids}` })
}
// 新增orientation
export const addOrientation = (data: OrientationSaveVO) => {
  return request.post({ url: '/learning/orientation', data})
}

// 查询orientation
export const detailOrientation = (id: number) => {
  return request.get({ url: `/learning/orientation/${id}` })
}

// 修改orientation
export const editOrientation = (data: OrientationSaveVO) => {
  return request.put({ url: '/learning/orientation', data})
}
export const AssignFun = (id: number, scope: number, data: []) => {
  return request.post({ url: `/learning/orientation/scope/assign?orientationId=${id}&scope=${scope}`, data})
}
// 查询orientation分配的课程
export const AssignedList = (params: OrientationAssignReqVO) => {
  return request.get<PageResult<OrientationAssignRespVO[]>>({ url: '/learning/orientation/scope/list', params })
}
// 删除orientation分配的数据
export const delAssign = (ids: number[]) => {
  return request.delete({ url: `/learning/orientation/scope/${ids}` })
}
/** 是否Display */
export const updateDisplay = (data: { id: number, display: boolean }) => {
  return request.put({ url: `/learning/orientation/updateDisplay?id=${data.id}&display=${data.display}`})
}
