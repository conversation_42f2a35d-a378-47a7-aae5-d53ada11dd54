import request from '@/config/axios'

export interface PaperReqVO {
  pageNo: number
  pageSize: number
  name: string
  status: number
  classifyId: number
  courseId: number
  questionNum: number
  totalScore: number
  isRandom: string
  singleChoiceScore: number
  multipleChoiceScore: number
  judgeScore: number
  deptId: number
  createTime: Date
  excludePaperIds: number
}

export interface PaperRespVO {
  id: number
  name: string
  status: number
  classifyId: number
  questionNum: number
  singleChoiceScore: number
  multipleChoiceScore: number
  judgeScore: number
  deptId: number
  createTime: Date
  totalScore: number
  banks: BankItem[]
  questions: QuestionItem[]
  isRandom: boolean
}

export interface BankItem {
  id: number
  paperId: number
  bankId: number
  sort: number
  singleChoiceNum: number
  multipleChoiceNum: number
  judgeNum: number
  createTime: Date
}

export interface QuestionItem {
  id: number
  paperId: number
  questionId: number
  sort: number
  score: number
  createTime: Date
}

export interface PaperSaveVO extends PaperReqVO {
  deptId: number
  id?: number
  judgeScore: number
  multipleChoiceScore: number
  questionNum: number
  isRandom: boolean
  singleChoiceScore: number
  totalScore: string
  banks: BankItem[]
  questions: QuestionItem[]
}
// 分页查询试卷
export const listPaper = (params: PaperReqVO) => {
  return request.get<PageResult<PaperRespVO[]>>({ url: '/learning/paper', params })
}

// 新增试卷
export const addPaper = (data: PaperSaveVO) => {
  return request.post({ url: '/learning/paper', data })
}

// 修改试卷
export const updatePaper = (data: PaperSaveVO) => {
  return request.put({ url: '/learning/paper', data })
}

// 删除试卷
export const delPaper = (paperIds: number[]) => {
  return request.delete({ url: `/learning/paper/${paperIds}` })
}
// 查询试卷信息
export const getPaper = (paperId: number) => {
  return request.get<IResponse<PaperRespVO>>({ url: `/learning/paper/${paperId}` })
}
// 禁用-启用试卷
export const updatePaperStatus = (paperId: number, status: string | number) => {
  return request.put({ url: `/learning/paper/${paperId}/status/${status}` })
}
