import request from '@/config/axios'

export interface JourneyReqVO {
  pageNo: number
  pageSize: number
  title: string
  categoryId: number
  keywords: string
  status: number
}
export interface JourneyRespVO {
  categoryId: boolean
  categoryName: string
  courseCount: number
  cover: string
  createBy: string
  id: number
  introduction: string
  keywords: string
  remark: string
  status: number
  title: string
  type: number
}
export interface JourneySaveVO {
  id?: string
  title: string
  status: number
  cover: string
  courseIds: string | number[] | undefined
  categoryId: number
  keywords: string | []
  introduction: string
}

export interface CourseInfoReqVO {
  id: number
  email: string
  onSchedule: boolean
  pageNum: number
  pageSize: number
  status: number
  studentName: string
}

// 上下架状态枚举
export enum JourneyStatusEnum {
  PUT_ON_SHELF = 1,
  MOVE_OFF_SHELF = 0
}

/**
 * 获取学习地图基本信息详情
 * @param journeyId 学习地图id
 * @returns 学习地图详情
 */
export const getJourney = (id: number) => {
  return request.get<IResponse<JourneyRespVO>>({ url: `/learning/journey/get?id=` + id })
}

// 查询学习地图列表
export const listJourney = (params: JourneyReqVO) => {
  return request.get<PageResult<JourneyRespVO>>({ url: '/learning/journey', params })
}

// 新增学习地图
export const addJourney = (data: JourneySaveVO) => {
  return request.post({ url: '/learning/journey', data})
}

// 修改学习地图
export const updateJourney = (data: JourneySaveVO) => {
  return request.put({ url: '/learning/journey', data})
}

// 删除学习地图
export const delJourney = (ids: number[]) => {
  return request.delete({ url: `/learning/journey/${ids}` })

}

// 上架/下架学习地图
export const putOnJourney = (ids: string[],status: number) => {
  return request.put({ url: `/learning/journey/update/status?ids=${ids}&status=${status}`})
}

/**
 * 给当前选中的数据分配可成
 * @param id 课程ID
 * @param scope 范围（公司，部门，员工）
 * @param type 课程类型（选修，必修）
 * @param data scope数据
 * @returns 分配结果
 */
export const AssignFun = (id: number, scope: number, type: number,data: any[]) => {
  return request.post({ url: `/learning/journey/assign?journeyId=${id}&scope=${scope}&type=${type}`, data})
}
/**
 * 查询已分配的课程
 */
export const AssignedList = (params: { journeyId: number, type: number, scope?: number }) => {
  return request.get({ url: '/learning/journey/assignedList', params })
}
/**
 * 获取当前课程学习情况列表 Todo
 * @param params 课程id
 * @returns 学习情况列表
 */
export const listStatistics = (params: CourseInfoReqVO) => {
  return request.get({ url: '/adapter/v1/course/info', params })
}

export const delAssign = (ids: string[]) => {
  return request.delete({ url: `/learning/journey/assign/${ids}` })
}
/**
 * 获取学生课程学习信息 Todo
 * @param courseId 课程ID
 * @param userId 学员ID
 * @returns 学员本课程学习统计信息
 */
export const getEmployeeStatistics = (courseId: number, userId: number) => {
  return request.get({ url: `/adapter/v1/course/chapter/info`, params: { courseId, userId} })
}

