import request from '@/config/axios'


export interface QuestionBankReqVO {
  pageNo: number
  pageSize: number
  name: string
  type?: string | number
}

export interface QuestionBankRespVO {
  id: number
  name: string
  classifyId: number
  questionNum: number
  singleChoiceNum: number
  multipleChoiceNum: number
  judgeNum: number
  deptId: number
  createTime: Date
}

export interface QuestionReqVO {
  pageNo: number
  pageSize: number
  bankId: number
  content: string
  remark: string
  right: boolean
  score: number
  sort: number
  type: number
}

export interface QuestionRespVO {
  id: number
  bankId: number
  type: number
  content: string
  remark: string
  right: boolean
  rightAnswer: string
  score: number
  sort: number
  answer: string
}

export interface QuestionSaveVO extends QuestionReqVO {
  id?: number
  rightAnswer: string
}

export interface QuestionBankSaveVO extends QuestionBankReqVO {
  classifyId: number
  deptId: number
  id?: number
  judgeNum: number
  multipleChoiceNum: number
  questionNum: number
  remark: string
  singleChoiceNum: number
}

// 试题类型枚举
export const QuestionTypeEnum = {
  // 类型（0：单选题，1：多选题，2：判断题）
  SINGLE_CHOICE: 0,
  MULTIPLE_CHOICE: 1,
  JUDGE: 2,
}
// 查询试题题库列表
export const listQustionBank = (params: QuestionBankReqVO) => {
  return request.get<PageResult<QuestionBankRespVO[]>>({ url: '/learning/question/bank', params })
}

// 新增试题题库
export const addQustionBank = (data: QuestionBankSaveVO) => {
  return request.post({ url: '/learning/question/bank', data})
}

// 修改试题题库
export const updateQustionBank = (data: QuestionBankSaveVO) => {
  return request.put({ url: '/learning/question/bank', data})
}

// 删除试题题库
export const delQustionBank = (ids: number[]) => {
  return request.delete({ url: `/learning/question/bank/${ids}` })
}
// 查询试题题库
export const getQustionBank = (id: number) => {
  return request.get({ url: `/learning/question/bank/${id}` })
}

// -----------------------------------------------
// 查询试题列表
export const listQustion = (params: QuestionReqVO) => {
  return request.get<PageResult<QuestionRespVO[]>>({ url: '/learning/question', params })
}
// 查询试题详情
export const InfoQustion = (id: number) => {
  return request.get({ url: `/learning/question/${id}` })
}
// 新增试题
export const addQustion = (data: QuestionSaveVO) => {
  return request.post({ url: '/learning/question', data})
}

// 修改试题
export const updateQustion = (data: QuestionSaveVO) => {
  return request.put({ url: '/learning/question', data})
}

// 删除试题
export const delQustion = (ids: number[]) => {
  return request.delete({ url: `/learning/question/${ids}` })
}
