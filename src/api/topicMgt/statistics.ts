import request from '@/config/axios'

export interface CourseOfStudentReqVO {
  userId: number
  name: string
  onSchedule: boolean
  pageNo: number
  pageSize: number
  status: number
  type: number
}

export interface CompanyPolicyEmpPageReqVO extends CourseOfStudentReqVO{
  title: string
  companyPolicyId: number
  empName: string
  empCode: string
  deptId: number
  ackStatus: number
  ackTime: string
  deptIds: number[]
  companyId: number
  ackType: number
}

export interface OnboardingEmpPageReqVO extends CourseOfStudentReqVO{
  categoryId: number
  empName: string
  empCode: string
}

// 查询专题课程列表
export const listCourseOfStudent = (params: CourseOfStudentReqVO) => {
  return request.get({ url: '/learning/course/pageOfStudent', params })
}

// 按人分页查询companyPolicy
export const listCompanyPolicyOfStudent = (params: CompanyPolicyEmpPageReqVO) => {
  return request.get({ url: '/learning/statistics/company/policy/emp/page', params })
}

// 按人分页查询onBoarding
export const listOnboardingOfStudent = (params: OnboardingEmpPageReqVO) => {
  return request.get({ url: '/learning/statistics/onBoarding/emp/page', params })
}
