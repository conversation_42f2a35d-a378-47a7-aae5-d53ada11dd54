import request from '@/config/axios'

/** ----- ENUM ----- */
export enum TrainingNeedStatusEnum {
  SUBMITTED = 2, // 提交
  APPROVED = 3, // 审批通过
  REJECTED = 4, // 审批拒绝
}

/** ----- INTERFACE ----- */
// 获得培训申请分页
export interface TrainingApplyPageReqVO {
  pageNo: number // 页码，从 1 开始（必填）
  pageSize: number // 每页条数，最大值为 100（必填）
  title?: string // 标题（可选）
  statusList?: number // 状态（可选，建议后期转为枚举）
  trainingType?: number // 培训类型（可选，建议后期转为枚举）
}

// 导出培训申请
export interface ExportTrainingApplyReqVO {}

export interface Content {
  id: number // 培训需求内容ID
  applyId: number // 申请ID
  skillName: string // 技能名称
  trainingType: number // 培训方式：1.MLC Training，2.Content course，3.On-Job Training
  contentCatalog: string // 内容目录(md格式)
}

// 培训申请详情
export interface TrainingApplyDetailVO {
  id: number // ID
  title: string // 标题
  positionName: string // 岗位名称
  content: Content[] // 内容
  status: number // 状态（1.草稿，2.提交，3.审批通过，4.审批拒绝，5.不予理睬）
  applicant: number // 申请人id
  applicantName: string // 申请人
  approveReason: string // 审批原因
  createTime: string // 创建时间
}

// 培训申请审批
export interface TrainingApplyVO {
  id: number // ID
  status: number // 状态（1.草稿，2.提交，3.审批通过，4.审批拒绝，5.不予理睬）
  approveReason?: string | null // 审批原因
}

/** ----- API ----- */
export const TrainingApplyApi = {
  // 获得培训申请分页
  getTrainingApplyPage: async (params: TrainingApplyPageReqVO) => {
    return await request.get({ url: `/edp/training-apply/page`, params })
  },

  // 获得培训申请
  getTrainingApply: async (id: number) => {
    return await request.get<TrainingApplyDetailVO>({ url: `/edp/training-apply/get?id=${id}` })
  },

  // 审批培训申请
  approveTrainingApply: async (data: TrainingApplyVO) => {
    return await request.put({ url: `/edp/training-apply/approve`, data })
  },

  // 导出培训申请 Excel
  exportTrainingApply: async (params) => {
    return await request.download({
      url: `/edp/training-apply/manager-export-excel`,
      params,
      responseType: 'blob'
    })
  }
}