import request from '@/config/axios'
/** ----- ENUM ----- */
/** 知识状态枚举 */
export enum KnowledgeStatusEnum {
  DRAFT = 1,        // 草稿
  UNPUBLISHED = 2,  // 待发布
  PUBLISHED = 3,    // 已发布
  REJECTED = 4      // 打回
}

/** ----- INTERFACE ----- */
/** 知识库 VO */
export interface KnowledgeVO {
  id?: number // ID
  title?: string // 标题
  content?: string // 内容
  introduction?: string // 简介
  keywords?: string // 关键词
  deptId?: number // 部门ID
  deptName?: string // 部门名称
  positionId?: number // 岗位ID
  chatId?: number // 聊天ID
  level?: number // 难度等级
  status?: number // 状态（1.草稿，2.待发布，3.已发布，4.打回）
  listed?: boolean // 是否上架(0.未上架，1.已上架)
  approver?: number // 审批人ID
  approveReason?: string // 审批原因
  version?: number // 发布的版本号
  source?: number // 来源（1.Employee，2.Manager）
}

/** 下载 VO */
export interface DownloadVO {
  id: number // 知识ID
  version: number // 版本号
  fileType: number // 文件类型（1.docx，2.pdf）
  status?: number // 状态
}

/** ----- API ----- */
export const KnowledgeApi = {
  // 查询知识分页
  getKnowledgePage: async (params: PageParam & KnowledgeVO) => {
    return await request.get<PageResult<KnowledgeVO[]>>({ url: `/edp/knowledge/page`, params })
  },

  // 查询知识详情
  getKnowledge: async (id: number) => {
    return await request.get<IResponse<KnowledgeVO>>({ url: `/edp/knowledge/get?id=` + id })
  },

  // 新增知识
  createKnowledge: async (data: KnowledgeVO) => {
    return await request.post({ url: `/edp/knowledge/create`, data })
  },

  // 修改知识
  updateKnowledge: async (data: KnowledgeVO) => {
    return await request.put({ url: `/edp/knowledge/update`, data })
  },

  // 发布知识
  publishKnowledge: async (data: KnowledgeVO) => {
    return await request.put({ url: `/edp/knowledge/publish`, data })
  },

  // 拒绝发布
  rejectKnowledge: async (data: KnowledgeVO) => {
    return await request.put({ url: `/edp/knowledge/reject`, data })
  },

  // 上下架
  listedKnowledge: async (data: KnowledgeVO) => {
    return await request.put({ url: `/edp/knowledge/listed`, data })
  },

  // 下载知识
  downloadKnowledge: async (params: DownloadVO) => {
    return await request.download({
      url: `/edp/knowledge/download`,
      params,
      responseType: 'blob'
    })
  },

  // 删除知识
  deleteKnowledge: async (id: number) => {
    return await request.delete({ url: `/edp/knowledge/delete?id=` + id })
  },
}