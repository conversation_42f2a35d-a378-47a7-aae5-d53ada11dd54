import request from '@/config/axios'

export interface jobDescListReqVO {
  pageNo?: number
  pageSize?: number
  positionId?: number
  positionName?: string
  status?: number
  version?: string
  chatId?: number
  enContent?: string
  chContent?: string
  arContent: string
  lastUpdateTime?: string
  createTime?: string
}

export interface JobDescReqVO {
  postId?: number
  compId?: number
  companyName?: string
  comShortname?: string
  deptId?: number
  deptName?: string
  depShortname?: string
  sectId?: number
  sectName?: string
  section?: SysSect
  postCode?: string
  postName?: string
  shortName?: string
  parentId?: number
  ancestors?: string
  orderNum?: number
  status?: string
  delFlag?: string
  dataSource?: string
  children?: SysPost[]
  pageNum?: number
  pageSize?: number
  params?: Record<string, any>
  createId?: number
  creator?: number
  createBy?: string
  createTime?: string
  updateId?: number
  updater?: number
  updateBy?: string
  updateTime?: string
  remark?: string
}

export interface downloadJDReqVO {
  id: number
  language: number
  fileType: number
}

export interface publishJDVersionReqVO {
  id: number | string
  positionId: number
  positionName: string
  status: number
  version: number
  chatId: number
  enContent: string
  chContent: string
  arContent: string
}

export interface TranslateJDReqVO {
  content: string
  originalLanguage: number
  targetLanguage: number
}

/**  查询JD列表 */
export const getJobDescList = (params: JobDescReqVO) => {
  return request.get({ url: '/edp/jd-version/post-list', params })
}

/* JD接口 */
export const JobDescApi = {
  // 获取JD版本分页
  getJDVersionList: async (params: jobDescListReqVO) => {
    return request.get({ url: 'edp/jd-version/page', params })
  },

  // 通过ID获取JD版本
  getJDVersionById: async (id: number) => {
    return request.get({
      url: 'edp/jd-version/get',
      params: { id }
    })
  },

  // 通过ID获取JD草稿
  getJDDraftById: async (id: number) => {
    return request.get({ url: 'edp/jd-version/apply', params: { id } })
  },

  // JD下载
  downloadJD: async (params: downloadJDReqVO) => {
    return request.download({ url: 'edp/jd-version/download', params, responseType: 'blob' })
  },

  // 保存JD草稿版本
  saveJD: async (data: publishJDVersionReqVO) => {
    return request.put({ url: 'edp/jd-version/update', data })
  },

  // 创建JD版本
  publishJD: async (data: publishJDVersionReqVO) => {
    return request.post({ url: 'edp/jd-version/create', data })
  },

  // 翻译JD
  translateJD: async (data: TranslateJDReqVO) => {
    return request.post({ url: 'edp/chat-history/translate', data })
  }
}