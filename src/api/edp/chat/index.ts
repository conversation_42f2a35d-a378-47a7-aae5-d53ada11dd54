import request from '@/config/axios'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getAccessToken } from '@/utils/auth'
import { config } from '@/config/axios/config'

export interface GetChatListReqVO extends PageParam {
  positionId: number
  positionName: string
  conversationId: number
  conversationNo: number
  title: string
  question: string
  answer: string
  source: number
  type: number
  createTime: string
  creator: string
  updateTime: string
  updater: string
}

export interface ChatReqVO {
  id: number
  positionId: number
  positionName: string
  conversationId: number
  conversationNo: number
  title: string
  question: string
  ocrContent: string
  answer: string
  source: number
  type: number
  fileOcrId: number
}

export interface OptimizeReqVO {
  positionId: number
  positionName: string
  question: string
  content: string
  source: number
  type: number
}

// AI对话接口
export const ChatMessageApi = {
  // 获得聊天会话分页
  getChatList: async (params: GetChatListReqVO) => {
    return request.get({ url: 'edp/chat-history/conv/page', params })
  },

  // 获得聊天记录分页
  getChatHistory: async (params: GetChatListReqVO) => {
    return request.get({ url: 'edp/chat-history/page', params })
  },

  // 创建会话
  createChat: async (params: { positionName: string }) => {
    return request.post({
      url: 'edp/chat-history/conv/create',
      params,
      headers: {
        token: 'ragflow-RkZjVlYjNjZTUxZTExZWY5N2I5MDI0Mm'
      }
    })
  },

  // 发送消息并获取AI回答内容
  getStreamAnswer: async (
    data: ChatReqVO,
    ctrl: AbortController,
    onMessage: (event: any) => void,
    onError: (event: any) => void,
    onClose: () => void
  ) => {
    const token = getAccessToken()
    return fetchEventSource(
      `${config.base_url}/edp/chat-history/conv/getStreamAnswer`,
      // `http://10.248.18.22:48080/admin-api/edp/chat-history/conv/getStreamAnswer`,
      {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          token: 'ragflow-RkZjVlYjNjZTUxZTExZWY5N2I5MDI0Mm'
        },
        body: JSON.stringify(data),
        openWhenHidden: true,
        onmessage: onMessage,
        onerror: onError,
        onclose: onClose,
        signal: ctrl.signal
      }
    )
  }
}

// JD优化接口
export const JDOptimizeApi = {
  // 获取JD优化结果
  getJDOptimization: async (
    data: OptimizeReqVO,
    ctrl: AbortController,
    onMessage: (event: any) => void,
    onError: (event: any) => void,
    onClose: () => void
  ) => {
    const token = getAccessToken()
    return fetchEventSource(
      `${config.base_url}/edp/chat-history/conv/getOptimizeAnswer`,
      // `http://10.248.18.22:48080/admin-api/edp/chat-history/conv/getStreamAnswer`,
      {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          token: 'ragflow-RkZjVlYjNjZTUxZTExZWY5N2I5MDI0Mm'
        },
        body: JSON.stringify(data),
        openWhenHidden: true,
        onmessage: onMessage,
        onerror: onError,
        onclose: onClose,
        signal: ctrl.signal
      }
    )
  }
}