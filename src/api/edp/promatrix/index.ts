import request from '@/config/axios'

/** 岗位列表查询参数 */
export interface PositionListReqVO extends PageParam {
  compId: number;   // 公司ID
  deptId: number;   // 部门ID
  sectId: number;   // 子部门ID
}

/** 岗位列表 */
export interface Position {
  postId: number;         // 岗位ID
  postName: string;       // 岗位名称
  initialized: boolean;   // 是否初始化：true=已初始化，false=未初始化
  published: boolean;     // 是否发布：true=已发布，false=未发布
  code: string;           // 岗位编码
  sort: number;           // 岗位排序
  status: number;         // 状态
  deptId: number;         // 所属部门ID
  deptName: string;       // 所属部门名称
  sectId: number;         // 所属子部门ID
  sectName: string;       // 所属子部门名称
  parentId: number;       // 父级岗位ID
}

export const proMatrixApi = {
  // 获取岗位列表
  getPositionList: async (params: PositionListReqVO) => {
    return request.get<PageResult<Position>>({ url: 'edp/position-learning-map/post-list', params })
  },
}