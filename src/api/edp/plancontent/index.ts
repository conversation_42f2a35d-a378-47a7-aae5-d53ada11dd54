import request from '@/config/axios'

// 计划内容 VO
export interface PlanContentVO {
  id: number // 计划内容ID
  recommendId: number // 撮合ID
  planId: number // 计划ID
  userId: number // 用户ID
  status: number // 内容状态：1.正常，2.新增，3.删除
  skipped: boolean // 是否跳过 0未跳过，1跳过
}

// 计划内容 API
export const PlanContentApi = {
  // 查询计划内容分页
  getPlanContentPage: async (params: any) => {
    return await request.get({ url: `/edp/plan-content/page`, params })
  },

  // 查询计划内容详情
  getPlanContent: async (id: number) => {
    return await request.get({ url: `/edp/plan-content/get?id=` + id })
  },

  // 新增计划内容
  createPlanContent: async (data: PlanContentVO) => {
    return await request.post({ url: `/edp/plan-content/create`, data })
  },

  // 修改计划内容
  updatePlanContent: async (data: PlanContentVO) => {
    return await request.put({ url: `/edp/plan-content/update`, data })
  },

  // 删除计划内容
  deletePlanContent: async (id: number) => {
    return await request.delete({ url: `/edp/plan-content/delete?id=` + id })
  },

  // 导出计划内容 Excel
  exportPlanContent: async (params) => {
    return await request.download({ url: `/edp/plan-content/export-excel`, params })
  }
}