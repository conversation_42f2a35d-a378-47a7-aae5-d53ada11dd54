import request from '@/config/axios'
import { config } from '@/config/axios/config'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getAccessToken } from '@/utils/auth'

export interface SkillListReqVO {
  jdId: number
  positionId: number
  positionName: string
  level: number
  question: string
  source: number
  type: number
}

export interface PositionSkillReqVO {
  id?: number
  positionId: number
  positionName: string
  positionReportsTo: string
  skills: {
    firstSkill: number
    secondSkill: number
    thirdSkill: string
  }[]
}

// 岗位技能标签接口
export const SkillApi = {
  // 发送消息并获取岗位技能标签
  getStreamAnswerAndSkills: async (
    data: SkillListReqVO,
    ctrl: AbortController,
    onMessage: (event: any) => void,
    onError: (event: any) => void,
    onClose: () => void
  ) => {
    // debugger
    const token = getAccessToken()
    return fetchEventSource(`${config.base_url}/edp/chat-history/skill/getStreamAnswer`, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(data),
      openWhenHidden: true,
      onmessage: onMessage,
      onerror: onError,
      onclose: onClose,
      signal: ctrl.signal
    })
  },

  // 获取上次生成的技能标签内容
  getLastSkills: async (positionId: number) => {
    return request.get({ url: `edp/chat-history/skill/getLastAnswer?positionId=${positionId}` })
  },

  // 创建岗位技能标签
  createPositionSkill: async (data: PositionSkillReqVO) => {
    return request.post({ url: '/edp/position-skill/create', data })
  },

  // 获得岗位技能标签
  getPositionSkills: async (positionId: number) => {
    return request.get({ url: '/edp/position-skill/get?positionId=' + positionId })
  },

  // 更新岗位技能标签
  updatePositionSkill: async (data: PositionSkillReqVO) => {
    return request.put({ url: '/edp/position-skill/update', data })
  }
}