import request from '@/config/axios'

/** ----- INTERFACE ----- */
/** 查询路线图列表 */
export interface CareerDevelopReqVO {
  deptId: number // 部门ID
  sectId?: number // 子部门ID
  positionId?: number // 岗位ID
  positionName?: string // 岗位名称
  parentId?: number // 上个节点ID
  root?: boolean // 是否为根节点，0否1是
}

// 职业发展 VO
export interface CareerDevelop {
  id?: number // ID
  positionId?: number // 岗位ID
  positionName?: string // 岗位名称
  parentId?: number // 上个节点ID
  root?: boolean // 是否为根节点，0否1是
  children?: CareerDevelop[]
}

// 职业发展详情 VO
export interface CareerDevelopDetail {
  id: number // ID
  positionId: number // 岗位ID
  positionName: string // 岗位名称
  parentId: number // 上个节点ID
  root: boolean // 是否为根节点，0否1是
  createTime: string // 创建时间
}

// 职业发展 API
export const CareerDevelopApi = {
  // 查询路线图列表
  getCareerDevelopPath: async (params: any) => {
    return await request.get<IResponse<CareerDevelop>[]>({
      url: `/edp/career-develop/list`,
      params
    })
  },

  // 查询职业发展详情
  getCareerDevelop: async (id: number) => {
    return await request.get<IResponse<CareerDevelopDetail>>({
      url: `/edp/career-develop/get?id=` + id
    })
  },

  // 查询可用岗位以新增岗位路径（查询同部门可创建新路线的岗位）
  getPositionsForNewPath: async (params: CareerDevelopReqVO) => {
    return await request.get<IResponse<CareerDevelopReqVO[]>>({
      url: `/edp/career-develop/get-positions-for-new-path`,
      params
    })
  },

  // 查询可用岗位以新增已有岗位路径上的节点（查询可添加的其他岗位）
  getPositionsForNewNode: async (params: CareerDevelopReqVO) => {
    return await request.get<IResponse<CareerDevelopReqVO[]>>({
      url: `/edp/career-develop/get-positions-for-new-node`,
      params
    })
  },

  // 新增职业发展结点
  createCareerDevelop: async (data: CareerDevelopReqVO) => {
    return await request.post({ url: `/edp/career-develop/create`, data })
  },

  // 删除职业发展结点
  deleteCareerDevelopNode: async (id: number) => {
    return await request.delete({ url: `/edp/career-develop/delete?id=` + id })
  },

  // 删除职业发展路径
  deleteCareerDevelopPath: async (rootNodeId: number) => {
    return await request.delete({ url: `/edp/career-develop/deletePath?rootNodeId=` + rootNodeId })
  }
}