import request from '@/config/axios'

// 学习计划 VO
export interface StudyPlanVO {
  id: number // 计划ID
  positionId: number // 岗位ID
  skillId: number // 技能ID
  userId: number // 用户ID
  title: string // 内容标题
  startDate: Date // 开始日期
  endDate: Date // 介绍
}

// 学习计划 API
export const StudyPlanApi = {
  // 查询学习计划分页
  getStudyPlanPage: async (params: any) => {
    return await request.get({ url: `/edp/study-plan/page`, params })
  },

  // 查询学习计划详情
  getStudyPlan: async (id: number) => {
    return await request.get({ url: `/edp/study-plan/get?id=` + id })
  },

  // 新增学习计划
  createStudyPlan: async (data: StudyPlanVO) => {
    return await request.post({ url: `/edp/study-plan/create`, data })
  },

  // 修改学习计划
  updateStudyPlan: async (data: StudyPlanVO) => {
    return await request.put({ url: `/edp/study-plan/update`, data })
  },

  // 删除学习计划
  deleteStudyPlan: async (id: number) => {
    return await request.delete({ url: `/edp/study-plan/delete?id=` + id })
  },

  // 导出学习计划 Excel
  exportStudyPlan: async (params) => {
    return await request.download({ url: `/edp/study-plan/export-excel`, params })
  }
}