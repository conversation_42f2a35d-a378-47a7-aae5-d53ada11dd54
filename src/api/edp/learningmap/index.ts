import request from '@/config/axios'

/** 岗位列表查询参数 */
export interface PositionListReqVO extends PageParam {
  compId: number;   // 公司ID
  deptId: number;   // 部门ID
  sectId: number;   // 子部门ID
}

/** 岗位列表 */
export interface Position {
  id: number;         // 岗位ID
  name: string;       // 岗位名称
  initialized: boolean;   // 是否初始化：true=已初始化，false=未初始化
  published: boolean;     // 是否发布：true=已发布，false=未发布
  code: string;           // 岗位编码
  sort: number;           // 岗位排序
  status: number;         // 状态
  deptId: number;         // 所属部门ID
  deptName: string;       // 所属部门名称
  sectId: number;         // 所属子部门ID
  sectName: string;       // 所属子部门名称
  parentId: number;       // 父级岗位ID
  skillSum?: number;      // 技能总数
  dataSource?: string;    // 数据源
  createBy?: string;      // 创建者
}

/** 学习地图查询参数 */
export interface LearningMapReqVO extends PageParam{
  positionId: number // 岗位ID，示例值(1)
  phase: number // 地图阶段(1: phase I，2: phase II，3: phase III)，示例值(1)
  contentTitle?: string // 内容标题，示例值(连能性能)
}


export interface InitPositionLearningMapReqVO {
    positionId: number,
    matchDegree: number
}

export interface publishPositionLearningMapReqVO {
    positionId: number,
    phase:number
}

export const positionLearningMapApi = {
  // 获取岗位列表
  getPositionList: async (params: PositionListReqVO) => {
    return request.get<PageResult<Position>>({ url: 'edp/position-learning-map/post-list', params })
  },

  // 查询某岗位的学习地图
  getLearningMap: async (positionId: number) => {
    return request.get<PageResult<any>>({ url: 'edp/position-learning-map/get?positionId=' + positionId })
  },

    // 初始化岗位学习地图
    initLearningMap: async (data: InitPositionLearningMapReqVO) => {
        return request.post({ url: 'edp/position-learning-map/init', data })
    },

    // 发布岗位学习地图
    publishLearningMap: async (data: publishPositionLearningMapReqVO) => {
        return request.put({ url: 'edp/position-learning-map/publish', data })
    }
}
