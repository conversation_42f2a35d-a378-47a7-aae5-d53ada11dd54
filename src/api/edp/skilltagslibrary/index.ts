import request from '@/config/axios'

// 技能标签库分页查询参数
export interface SkillTagsLibraryPageReqVO {
  id?: number // ID
  pageNo: number // 页码
  pageSize: number // 页大小
  deptId?: number // 部门ID
  deptName?: string // 部门名称
  firstSkill?: number // 一级标签
  secondSkill?: number // 二级标签
  thirdSkill?: string // 三级标签
  status?: boolean // 状态（0.禁用 1.启用）
  createTime?: string // 创建时间
}

// 技能标签库 VO
export interface SkillTagsLibraryVO {
  id?: number // ID
  deptId: number // 部门id
  deptName: string // 部门名称
  firstSkill: number // 一级标签
  secondSkill: number // 二级标签
  thirdSkill: string // 三级标签
  status: boolean // 状态（0.禁用 1.启用）
}

// 技能标签库 API
export const SkillTagsLibraryApi = {
  // 查询技能标签库分页
  getSkillTagsLibraryList: async (params: SkillTagsLibraryPageReqVO) => {
    return await request.get({ url: `/edp/skill-library/page`, params })
  },

  // 查询技能标签库详情
  getSkillTagsLibrary: async (id: number) => {
    return await request.get({ url: `/edp/skill-library/get?id=` + id })
  },

  // 新增技能标签库
  createSkillTagsLibrary: async (data: SkillTagsLibraryVO) => {
    return await request.post({ url: `/edp/skill-library/create`, data })
  },

  // 修改技能标签库
  updateSkillTagsLibrary: async (data: SkillTagsLibraryVO) => {
    return await request.put({ url: `/edp/skill-library/update`, data })
  },

  // 启用/禁用技能标签库
  toggleSkillLibraryStatus: async (id: number, status: boolean) => {
    return await request.put({ url: `edp/skill-library/shelve?id=` + id + `&status=` + status })
  },

  // 删除技能标签库
  deleteSkillTagsLibrary: async (id: number) => {
    return await request.delete({ url: `/edp/skill-library/delete?id=` + id })
  },

  // 导出技能标签库 Excel
  exportSkillTagsLibrary: async () => {
    return await request.download({ url: `/edp/skill-library/export-excel` })
  }
}