import request from '@/config/axios'

export interface BannerReqVO {
  status: number
  title: string
  pageNo: number
  pageSize: number
  summary?: string
  image?: string
  details?: string
}

export interface BannerRespVO {
  id: number
  title: string
  summary: string
  image: string
  details: string
  sort: number
  status: number
  createTime: Date
  createBy: string
  updateTime: Date
}

export interface BannerSaveVO {
  id?: number
  title: string
  summary: string
  image: string
  details: string
  sort: number
  status: number
}

/**
 * 获取banner列表
 * @param params 查询参数
 * @returns banner列表
 */
export const listBanner = (params: BannerReqVO) => {
  return request.get({ url: '/system/banner', params })
}
/**
 * 查询banner详情
 * @param id bannerid
 * @returns banner详情
 */
export const getBanner = (id: number) => {
  return request.get({ url: `/system/banner/${id}` })
}
/**
 * 修改banner
 * @param data 修改banner
 * @returns 修改结果
 */
export const updateBanner = (data: BannerSaveVO) => {
  return request.put({ url: '/system/banner', data})
}
/**
 * 删除banner
 * @param ids id数组
 * @returns 删除结果
 */
export const delBanner = (ids: string[]) => {
  return request.delete({ url: `/system/banner/${ids}` })
}
/**
 * 新增banner
 * @param data banner数据
 * @returns 添加结果
 */
export const addBanner = (data: BannerSaveVO) => {
  return request.post({ url: '/system/banner', data})
}
