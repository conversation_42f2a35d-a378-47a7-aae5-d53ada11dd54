import request from '@/config/axios'

/** 获取顶部total数量 */
export const getTotal = () => {
  return request.get({ url: '/learning/dashboard/number' })
}

/** 获取柱状图数据 */
export const getCourseBar = () => {
  return request.get({ url: '/learning/dashboard/topic-course-number' })
}
/**
 * 根据开始日期与结束日期筛选登录信息
 * @param startTime 开始日期(yyyy-MM-dd hh:mm:ss)
 * @param endTime 结束日期(yyyy-MM-dd hh:mm:ss)
 * @returns 筛选后的登录信息
 */
export const getLoginRecord = (startTime: string, endTime: string) => {
  return request.get({ url: '/system/login-log/statistics', params: { startTime, endTime } })
}
/**
 * 查询公司用户统计信息
 * @returns 公司用户统计信息
 */
export const getUserRecord = () => {
  return request.get({ url: '/system/user/company-employee-statistics' })
}
