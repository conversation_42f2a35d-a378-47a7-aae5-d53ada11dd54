export default {
  common: {
    inputText: 'Please input',
    uploadText: 'Please upload',
    selectText: 'Please select',
    chooseText: 'Please choose',
    choose: 'Choose',
    noData: 'No Data',
    startTimeText: 'Start time',
    endTimeText: 'End time',
    login: 'Login',
    required: 'This is required',
    loginOut: 'Login out',
    profile: 'User Center',
    reminder: 'Reminder',
    loginOutMessage: 'Exit the system?',
    back: 'Back',
    ok: 'OK',
    save: 'Save',
    cancel: 'Cancel',
    close: 'Close',
    reload: 'Reload current',
    success: 'Success',
    closeTab: 'Close current',
    closeTheLeftTab: 'Close left',
    closeTheRightTab: 'Close right',
    closeOther: 'Close other',
    closeAll: 'Close all',
    prevLabel: 'Prev',
    nextLabel: 'Next',
    skipLabel: 'Jump',
    doneLabel: 'End',
    menu: 'Menu',
    menuDes: 'Menu bar rendered in routed structure',
    collapse: 'Collapse',
    collapseDes: 'Expand and zoom the menu bar',
    tagsView: 'Tags view',
    tagsViewDes: 'Used to record routing history',
    tool: 'Tool',
    toolDes: 'Used to set up custom systems',
    query: 'Query',
    reset: 'Reset',
    shrink: 'Put away',
    expand: 'Expand',
    confirmTitle: 'System Hint',
    exportMessage: 'Whether to confirm export data item?',
    importMessage: 'Whether to confirm import data item?',
    createSuccess: 'Create Success',
    updateSuccess: 'Update Success',
    delMessage: 'Delete the selected data?',
    delDataMessage: 'Delete the data?',
    delNoData: 'Please select the data to delete',
    delSuccess: 'Deleted successfully',
    delOption: 'Confirm to delete the selected option？',
    keyWords: 'Key words cannot be repeated.',
    keyWordsLength: 'You can set up to five keywords, and a single keyword can have up to 300 English characters.',
    isRecommend: 'Recommendation:',
    uploadMessage: 'Uploading file, please wait...',
    uploadFormat: 'Format supports:',
    uploadError: 'Loading failed',
    noScormLink: 'No Scorm playing link was obtained',
    uploadLoadingError: 'Loading failed, please check if the network is working properly.',
    fromRepository: 'From Repository',
    source: 'Source:',
    language: 'Language:',
    duration: 'Duration:',
    format: 'Format:',
    size: 'Size:',
    subject: 'Subject:',
    type: 'Type:',
    uploadFormatMessage: 'Format supports:Zip,Audio,Video,PDF,PPT,Word',
    uploadFormatMessage2: 'Format supports:PDF',
    uploadFormatMessage3: 'Format supports: doc/txt/pdf/csv/xls/ppt',
    noExist: 'Note: The resource already exists  and no need to be uploaded again.',
    inputNumberText: 'Please input number',
    selectTeachingForm: 'Select teaching form',
    selectTeachingContent: 'Select teaching content',
    learningSetting: 'Learning Setting',
    noSubject: 'No Subject',
    unzip: 'Unzipping',
    failure: 'Failure',
    nonScorm: 'Non-Scorm',
    updateFailure: 'For updating, please off the shelves first.',
    incompatible: 'Incompatible',


    index: 'Index',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    copy: 'Copy',
    copySuccess: 'Copy Success',
    copyError: 'Copy Error',
    upload: 'Please upload a file',
    uploadSuccessLength: 'Number of successful uploads: ',
    updateSuccessLength: 'Number of successful updates: ',
    updateFailLength: 'Number of failed updates: ',
    selectAll: 'Select All/Unselect All',
    expandAll: 'Expand All/Collapse All',
    yes: 'Yes',
    no: 'No',
    expandSwitch: 'Expand',
    collapseSwitch: 'Collapse',
    loading: 'Loading, please wait',
    linkage: 'Parent-child linkage (selecting the parent node automatically selects the child nodes):',
    message: 'The route address being accessed, e.g., `user`. For externalTraining network addresses, it should start with `http(s)://`',
    menuPath: 'Route Address',
    menuMessage: "Permission character on the Controller method, e.g., @PreAuthorize(`@ss.hasPermission('system:user:list')`)",
    menuShowStatusMessage: 'When hidden is selected, the route will not appear in the sidebar but can still be accessed',
    menuAlwaysShowMessage: 'When not selected, if the menu has only one sub-menu, it will not display itself and will directly display the sub-menu',
    menuCacheStatusMessage: 'When cached is selected, it will be cached by `keep-alive`, and the "Component Name" field must be filled in',
    data: 'Data',
    default: 'Default',
    primary: 'Primary',
    warning: 'Warning',
    info: 'Info',
    danger: 'Danger',
    catalog: 'Catalog',
    button: 'Button',
    pleaseInput: 'Please input',
    parameter: 'Parameter',
    cannotBeEmpty: 'Cannot be empty',
    searchMenuContent: 'Please enter the menu content',
    adminMenuName: 'Admin End',
    studentMenuName: 'User End',
    scan: 'Scan',
    result: 'Result',
    batchAction: 'Batch Action',
    ddtPermitNo: 'DDT Permit NO.',
    dateOfBirth: 'D.O.B',
    projectAsset: 'Project/Asset',
    workType: 'Work Type',
    workTerm: 'Work Term',
    drivingLicenceNumber: 'Driving Licence Number',
    issuingDate: 'Issuing Date',
    expiryDate: 'Expiry Date',
    vehicle: 'Vehicle',
    eyeTest: 'Eye Test',
    testResult: 'Test Result',
    reject: 'Reject',
    pass: 'Pass',
    fail: 'Fail',
    postpone: 'Postpone',
    batchReject: 'Batch Reject',
    batchPass: 'Batch Pass',
    batchFail: 'Batch Fail',
    batchPostpone: 'Batch Postpone',
    successfulOperation: 'Successful operation',
    projectQRCode: 'Project QR Code',
    downloadQRCode: 'Download QR Code',
    launch: 'Launch',
    uploadFile: 'Upload',
    materialName: 'Material Name',
    creationTime: 'Creation Time',
    uploadMaterials: 'Upload Materials',
    video: 'Video',
    pdf: 'PDF',
    pocEmail: 'POC Email',
    pocPhoneNumber: 'POC Phone number',
    contractHolder: 'Contract Holder',
    ePassportNo: 'E-Passport No.',
    bookingMode: 'Booking Mode',
    batchRemove: 'Batch Remove',
    cantQueryProcessInfo: "Can't query the process information!",
    preferredDate: 'Preferred Date',
    receivedDate: 'Received Date',
    courseTitle: 'Course Title',
    workTerms: 'Work Terms',
    assign: 'Assign',
    batchAssign: 'Batch Assign',
    pleaseSelectSameCourse: 'Please select students with the same course',
    startDate: 'Start Date',
    endDate: 'End Date',
    publishStatus: 'Publish Status',
    classTitle: 'Class Title',
    classType: 'Class Type',
    trainer: 'Trainer',
    classroom: 'Classroom',
    date: 'Date',
    bookingNumber: 'Booking Number',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    nextWeek: 'Next Week',
    thisYear: 'This Year',
    allocationSuccessful: 'Allocation successful',
    start: 'start',
    end: 'end',
    advancedScreening: 'Advanced Filtering',
    clear: 'Clear'
  },
  lock: {
    lockScreen: 'Lock screen',
    lock: 'Lock',
    lockPassword: 'Lock screen password',
    unlock: 'Click to unlock',
    backToLogin: 'Back to login',
    entrySystem: 'Entry the system',
    placeholder: 'Please enter the lock screen password',
    message: 'Lock screen password error'
  },
  error: {
    noPermission: `Sorry, you don't have permission to access this page.`,
    pageError: 'Sorry, the page you visited does not exist.',
    networkError: 'Sorry, the server reported an error.',
    returnToHome: 'Return to home',
    uploadError: 'Upload failed, please try uploading again!',
    uploadErrorLength: 'You can upload a maximum of one file only!',
    pathError: 'The path must start with /',
    pathErrorStart: 'The path cannot start with /',

    fileFormatError: 'The file format is incorrect, please upload',
    imageFormatFile: `image format file!`,
    imageSizeError: 'The size of the uploaded avatar image cannot exceed',
    imageSizeErrorLength: 'Please upload pictures according to the specified size',
    imageLoading: 'Image loading failure',
    fileLengthError: 'The number of uploaded files cannot exceed',
    uploadErrorMessage: 'Failed to upload pictures',
    pleaseSetDuration: 'Please set the duration',
    fileTypeError: 'The uploaded file does not match the specified type',
    fileSizeError: 'The size is not more than',
    m: 'M!',
    failedToUploadFile: 'Failed to upload file',
    dropFileHereOr: 'Drop file here or',
    pleaseChooseExamPaper: 'Please choose exam paper',
    fileFormatIsNotCorrect: 'File format is not correct, please upload',
    formatFile: `format file!`,
    fileSizeIsNotCorrect: 'Image format error',
    fileSizeIsNotCorrectLength: 'Upload file size cannot exceed',
    imageInsertionFailed: 'Image insertion failed',
    uploadTo: 'You can upload up to',
    resourcesAtATime: ' resources at a time!',
    positionNotGenerated: 'This position has not generated skills, learning map generation is not available',
    positionAlreadyGenerated: 'This position has already generated a learning map, would you like to go directly to the learning content?'


  },
  loading: {
    upLoading: 'Uploading pictures, please wait...'
  },
  permission: {
    hasPermission: `Please set the operation permission label value`,
    hasRole: `Please set the role permission tag value`
  },
  setting: {
    projectSetting: 'Project setting',
    theme: 'Theme',
    layout: 'Layout',
    systemTheme: 'System theme',
    menuTheme: 'Menu theme',
    interfaceDisplay: 'Interface display',
    breadcrumb: 'Breadcrumb',
    breadcrumbIcon: 'Breadcrumb icon',
    collapseMenu: 'Collapse menu',
    hamburgerIcon: 'Hamburger icon',
    screenfullIcon: 'Screenfull icon',
    sizeIcon: 'Size icon',
    localeIcon: 'Locale icon',
    messageIcon: 'Message icon',
    tagsView: 'Tags view',
    logo: 'Logo',
    greyMode: 'Grey mode',
    fixedHeader: 'Fixed header',
    headerTheme: 'Header theme',
    cutMenu: 'Cut Menu',
    copy: 'Copy',
    clearAndReset: 'Clear cache and reset',
    copySuccess: 'Copy success',
    copyFailed: 'Copy failed',
    footer: 'Footer',
    uniqueOpened: 'Unique opened',
    tagsViewIcon: 'Tags view icon',
    reExperienced: 'Please exit the login experience again',
    fixedMenu: 'Fixed menu',
    myInternalMessage: 'My internalTraining message',
    viewAll: 'ALL',
    // 轮播图
    banner: {
      status: 'Shelf state',
      title: 'Title',
      no: 'No.',
      updateTime: 'Update time',
      basicInfo: 'Basic Information',
      maxSize: '(Maximum 4 entries of “Shelf state”)',
      titlePH: 'Please enter the news title,less than 50 words',
      cover: 'Cover',
      coverPH: '990*310pixels，support PNG、JPG、GIF,less than 5M',
      summary: 'Abstract',
      summaryPH: 'Please enter the news abstract,less than 5000 words',
      detail: 'Details',
      titleRule: 'Title cannot be empty',
      coverRule: 'Cover cannot be empty',
      sort: 'Sort',
      onShelfTime: 'On shelf time'
    },
  },
  size: {
    default: 'Default',
    large: 'Large',
    small: 'Small'
  },
  login: {
    welcome: 'Welcome to the system',
    message: 'Backstage management system',
    tenantname: 'TenantName',
    username: 'Username',
    password: 'Password',
    code: 'verification code',
    login: 'Sign in',
    relogin: 'Sign in again',
    otherLogin: 'Sign in with',
    register: 'Register',
    checkPassword: 'Confirm password',
    remember: 'Remember me',
    hasUser: 'Existing account? Go to login',
    forgetPassword: 'Forget password?',
    tenantNamePlaceholder: 'Please Enter Tenant Name',
    usernamePlaceholder: 'Please Enter Username',
    passwordPlaceholder: 'Please Enter Password',
    codePlaceholder: 'Please Enter Verification Code',
    mobileTitle: 'Mobile sign in',
    mobileNumber: 'Mobile Number',
    mobileNumberPlaceholder: 'Plaease Enter Mobile Number',
    backLogin: 'back',
    getSmsCode: 'Get SMS Code',
    btnMobile: 'Mobile sign in',
    btnQRCode: 'QR code sign in',
    qrcode: 'Scan the QR code to log in',
    btnRegister: 'Sign up',
    SmsSendMsg: 'code has been sent'
  },
  captcha: {
    verification: 'Please complete security verification',
    slide: 'Swipe right to complete verification',
    point: 'Please click',
    success: 'Verification succeeded',
    fail: 'verification failed'
  },
  router: {
    login: 'Login',
    home: 'Dashboard',
    dictName: 'Dictionary Data',
    analysis: 'Analysis',
    workplace: 'Workplace',
    addBanner: 'Add Banner',
    editBanner: 'Edit Banner',
    contentCourse: 'Content Course',
    importList: 'Importing List',
    addJourney: 'Add/Edit Journey',
    addOnBoarding: 'Add/Edit Onboarding',
    learningRecords: 'Learning Records',
    addCompanyPolicy: 'Add/Edit CompanyPolicy',
    addOrientation: 'Add/Edit Orientation',
    createExam: 'Create Exam',
    editExam: 'Edit Exam',
    viewExam: 'View Exam',
    examRecord: 'Exam Record',
    addCustomizedPaper: 'Add Customized Paper',
    addAutoPaper: 'Add Auto Paper',
    editPaper: 'Edite Paper',
    questionMgt: 'Question management',
    addQuestion: 'Question add',
    editQuestion: 'Question edite',
    courseDetail: 'Course Detail',
    onboardingDetail: 'Onboarding Detail',
    companyPolicyDetail: 'Company Policy Detail',
    examDetail: 'Exam Detail',
    studentDetail: 'Student Detail',
    manage: 'Manage'
  },
  analysis: {
    newUser: 'New user',
    unreadInformation: 'Unread information',
    transactionAmount: 'Transaction amount',
    totalShopping: 'Total Shopping',
    monthlySales: 'Monthly sales',
    userAccessSource: 'User access source',
    january: 'January',
    february: 'February',
    march: 'March',
    april: 'April',
    may: 'May',
    june: 'June',
    july: 'July',
    august: 'August',
    september: 'September',
    october: 'October',
    november: 'November',
    december: 'December',
    estimate: 'Estimate',
    actual: 'Actual',
    directAccess: 'Airect access',
    mailMarketing: 'Mail marketing',
    allianceAdvertising: 'Alliance advertising',
    videoAdvertising: 'Video advertising',
    searchEngines: 'Search engines',
    weeklyUserActivity: 'Weekly user activity',
    activeQuantity: 'Active quantity',
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday'
  },
  workplace: {
    welcome: 'Hello',
    happyDay: 'Wish you happy every day!',
    toady: `It's sunny today`,
    notice: 'Announcement',
    project: 'Project',
    access: 'Project access',
    toDo: 'To do',
    introduction: 'A serious introduction',
    shortcutOperation: 'Quick entry',
    operation: 'Operation',
    index: 'Index',
    personal: 'Personal',
    team: 'Team',
    quote: 'Quote',
    contribution: 'Contribution',
    hot: 'Hot',
    yield: 'Yield',
    dynamic: 'Dynamic',
    push: 'push',
    follow: 'Follow'
  },
  form: {
    input: 'Input',
    inputNumber: 'InputNumber',
    default: 'Default',
    icon: 'Icon',
    mixed: 'Mixed',
    textarea: 'Textarea',
    slot: 'Slot',
    position: 'Position',
    autocomplete: 'Autocomplete',
    select: 'Select',
    selectGroup: 'Select Group',
    selectV2: 'SelectV2',
    cascader: 'Cascader',
    switch: 'Switch',
    rate: 'Rate',
    colorPicker: 'Color Picker',
    transfer: 'Transfer',
    render: 'Render',
    radio: 'Radio',
    button: 'Button',
    checkbox: 'Checkbox',
    slider: 'Slider',
    datePicker: 'Date Picker',
    shortcuts: 'Shortcuts',
    today: 'Today',
    yesterday: 'Yesterday',
    aWeekAgo: 'A week ago',
    week: 'Week',
    year: 'Year',
    month: 'Month',
    dates: 'Dates',
    daterange: 'Date Range',
    monthrange: 'Month Range',
    dateTimePicker: 'DateTimePicker',
    dateTimerange: 'Datetime Range',
    timePicker: 'Time Picker',
    timeSelect: 'Time Select',
    inputPassword: 'input Password',
    passwordStrength: 'Password Strength',
    operate: 'operate',
    change: 'Change',
    restore: 'Restore',
    disabled: 'Disabled',
    disablement: 'Disablement',
    delete: 'Delete',
    add: 'Add',
    setValue: 'Set value',
    resetValue: 'Reset value',
    set: 'Set',
    subitem: 'Subitem',
    formValidation: 'Form validation',
    verifyReset: 'Verify reset',
    remark: 'Remark',
// --------User Module----------
    userNickName: 'User Nickname',
// Belonging department
    belongingDept: 'Belonging Department',
    phoneNumber: 'Phone Number',
    userName: 'User Name',
    userPassword: 'User Password',
    email: 'Email',
    userSex: 'User Gender',
    posts: 'Position',

// --------Role Module----------
    roles: 'Roles',
    roleName: 'Role Name',
    roleType: 'Role Type',
    roleKey: 'Role Key',
    showSort: 'Display Order',
    status: 'Status',

// --------Menu Module----------
    menuPermission: 'Menu Permission',
    permissionScope: 'Permission Scope',
    menuCode: 'Menu Code',
    menu: 'Menu Name',
    menuName: 'Chinese Menu Name',
    menuNameEn: 'English Menu Name',
    menuNameAr: 'Arabic Menu Name',
    previousMenu: 'Parent Menu',
    menuType: 'Menu Type',
    menuIcon: 'Menu Icon',
    menuPath: 'Route Address',
    menuComponent: 'Component Path',
    menuComponentName: 'Component Name',
    menuPermissionKey: 'Permission Key',
    menuSort: 'Display Sort',
    menuStatus: 'Menu Status',
    menuShowStatus: 'Show Status',
    show: 'Show',
    hide: 'Hide',
    alwaysShow: 'Always Show',
    always: 'Always',
    not: 'Not',
    cacheStatus: 'Cache Status',
    cache: 'Cache',
    noCache: 'No Cache',

// --------Department Module----------
    deptName: 'Department Name',
    deptStatus: 'Department Status',
    parentDept: 'Parent Department',
    deptShowSort: 'Display Sort',
    deptNickName: 'Responsible Person',
    deptPhone: 'Contact Phone',
    deptEmail: 'Email',

// --------Post Module----------
    postName: 'Position Name',
    postCode: 'Position Code',
    postSort: 'Position Order',

// --------Dictionary Module----------
    dictName: 'Dictionary Name',
    dictType: 'Dictionary Type',
    createTime: 'Creation Time',
    dictLabel: 'Dictionary Label',
    dictLabelCn: 'Chinese Dictionary Label',
    dictLabelEn: 'English Dictionary Label',
    dictLabelAr: 'Arabic Dictionary Label',
    dictValue: 'Dictionary Key Value',
    dictShowSort: 'Dictionary Sort',
    dictColor: 'Color Type',
    cssClass: 'CSS Class',

// --------Message Center----------
// SMS Management Module (SMS Channel)
    signature: 'SMS Signature',
    enableStatus: 'Enable Status',
    channelCode: 'Channel Code',
    channelStatus: 'Enable Status',
    channelApiKey: 'SMS API Account',
    channelApiSecret: 'SMS API Secret',
    channelCallbackUrl: 'SMS Send Callback URL',

// SMS Management Module (SMS Template)
    smsType: 'SMS Type',
    openStatus: 'Open Status',
    templateCode: 'Template Code',
    templateId: 'SMS API Template ID',
    templateContent: 'SMS Template',
    templateChannel: 'SMS Channel',
    templateChannelCode: 'SMS Channel Code',
    templateName: 'Template Name',
    templateSubstance: 'Template Content',
    mobile: 'Phone Number',

// SMS Management Module (SMS Log)
    logTemplateCode: 'Template Code',
    sendStatus: 'Send Status',
    sendTime: 'Send Time',
    receiveStatus: 'Receive Status',
    receiveTime: 'Receive Time',

// Email Management Module (Email Template)
    receiveEmail: 'Recipient Email',

// --------Internal Message Management----------
// Internal Message Management ===> Template Management
    senderName: 'Sender Name',
    type: 'Type',
    emailUserType: 'User Type',
    recipientID: 'Recipient ID',
    recipient: 'Recipient',

// Internal Message Management ===> Message Records
    userId: 'User ID',
    templateType: 'Template Type',

// --------Notice Announcement----------
    noticeTitle: 'Notice Title',
    noticeContent: 'Notice Content',
    noticeStatus: 'Notice Status',
    noticeType: 'Notice Type',

// --------Tenant List----------
    tenantName: 'Tenant Name',
    contactName: 'Contact Name',
    contactPhone: 'Contact Phone',
    tenantStatus: 'Tenant Status',
    tenantPackage: 'Tenant Package',
    tenantQuota: 'Account Quota',
    expireTime: 'Expiration Time',
    domain: 'Bound Domain',

// --------Tenant Package----------
    packageName: 'Package Name',

    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramName: 'Parameter Name',
    paramKey: 'Parameter Key',
    paramType: 'Built-in System Type',
    paramCategory: 'Parameter Category',
    paramValue: 'Parameter Value',
    paramVisible: 'Is Visible',
    // --------我的站内信----------
    readStatus: 'Has it been read',
  },
  watermark: {
    watermark: 'Watermark'
  },
  table: {
    table: 'Table',
    index: 'Index',
    title: 'Title',
    author: 'Author',
    createTime: 'Create Time',
    action: 'Action',
    pagination: 'pagination',
    reserveIndex: 'Reserve index',
    restoreIndex: 'Restore index',
    showSelections: 'Show selections',
    hiddenSelections: 'Restore selections',
    showExpandedRows: 'Show expanded rows',
    hiddenExpandedRows: 'Hidden expanded rows',
    header: 'Header',
    // --------User Module----------
    userNumber: 'User Number',
    userName: 'User Name',
    userNickName: 'User Nickname',
    deptName: 'Department',
    phoneNumber: 'Phone Number',
    status: 'Status',

    // --------Role Module----------
    roleNumber: 'Role Number',
    roleName: 'Role Name',
    roleType: 'Role Type',
    roleKey: 'Role Key',
    showSort: 'Display Order',
    remark: 'Remarks',

    // --------Menu Module----------
    menuName: 'Menu Name',
    menuIcon: 'Menu Icon',
    menuSort: 'Sort',
    menuPermission: 'Permission Identifier',
    menuComponent: 'Component Path',
    menuComment: 'Component Name',
    menuStatus: 'Menu Status',

    // --------Department Module----------
    nickName: 'Responsible Person',
    deptSort: 'Sort Order',

    // --------Post Module----------
    postNumber: 'Post Number',
    postName: 'Post Name',
    postCode: 'Post Code',
    postSort: 'Post Sort Order',
    postRemark: 'Post Remarks',

    // --------Dictionary Module----------
    dictNumber: 'Dictionary Number',
    dictName: 'Dictionary Name',
    dictType: 'Dictionary Type',
    dictLabel: 'Dictionary Label',
    dictLabelCn: 'Chinese Dictionary Label',
    dictLabelEn: 'English Dictionary Label',
    dictLabelAr: 'Arabic Dictionary Label',
    dictValue: 'Dictionary Key Value',
    dictSort: 'Dictionary Sort Order',
    dictColor: 'Color Type',
    cssClass: 'CSS Class',

    // --------Message Center----------
    // SMS Management Module (SMS Channel)
    channelId: 'ID',
    channelCode: 'Channel Code',
    smsSign: 'SMS Signature',
    channelStatus: 'Enabled Status',
    channelApiKey: 'SMS API Account',
    channelApiSecret: 'SMS API Secret',
    channelCallbackUrl: 'SMS Send Callback URL',

    // SMS Management Module (SMS Template)
    templateName: 'Template Name',
    templateCode: 'Template Code',
    templateContent: 'Template Content',
    templateTitle: 'Template Title',
    templateType: 'Template Type',
    templateApiTemplateCode: 'SMS API Template Code',
    templateChannel: 'SMS Channel',

    // SMS Management Module (SMS Log)
    templateNumber: 'ID',
    logSmsContent: 'SMS Content',
    logSendStatus: 'Send Status',
    logReceiveStatus: 'Receive Status',
    logTemplateCode: 'Template Code',

    // Email Management Module (Email Account)
    emailAccount: 'Email Account',
    email: 'Email',
    emailUsername: 'User Name',
    emailPassword: 'Email Password',
    emailSmtpHost: 'SMTP Server Domain',
    emailSmtpPort: 'SMTP Server Port',
    emailSsl: 'Enable SSL',
    emailStarttlsEnable: 'Enable STARTTLS',

    // Email Management Module (Email Template)
    emailFromName: 'Sender Name',

    // Email Management Module (Email Record)
    emailUserType: 'User Type',
    emailTitle: 'Email Title',
    emailContent: 'Email Content',
    emailParams: 'Email Parameters',
    emailAddress: 'Sender Email Address',
    emailTemplateFromName: 'Template Sender Name',
    emailSendNumber: 'Sent Message ID',
    emailSendException: 'Send Exception',
    sendStatus: 'Send Status',
    receiveEmail: 'Recipient Email',

    // --------Internal Message Management----------
    // Internal Message Management ===> Template Management
    type: 'Type',
    senderName: 'Sender Name',
    openStatus: 'Enabled Status',

    // Internal Message Management ===> Message Records
    templateParams: 'Template Parameters',
    readStatus: 'Read Status',
    readTime: 'Read Time',

    // --------Notice Announcement----------
    noticeNumber: 'Notice Number',
    noticeTitle: 'Notice Title',
    noticeType: 'Notice Type',

    // --------Tenant List----------
    tenantNumber: 'Tenant Number',
    tenantName: 'Tenant Name',
    tenantPackage: 'Tenant Package',
    sysTenant: 'System Tenant',
    contactName: 'Contact Name',
    contactPhone: 'Contact Phone',
    tenantStatus: 'Tenant Status',
    tenantQuota: 'Account Quota',
    domain: 'Bound Domain',

    // --------Tenant Package----------
    tenantPackageNumber: 'Package Number',
    tenantPackageName: 'Package Name',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramId: 'Parameter ID',
    paramCategory: 'Parameter Category',
    paramName: 'Parameter Name',
    paramKey: 'Parameter Key',
    paramValue: 'Parameter Value',
    paramVisible: 'Is Visible',
    paramClass: 'Built-in System Class',
    // --------我的站内信----------
    sendName: 'Send Name',
    sendTime: 'Send Time',
    messageContent: 'Content',
    detail: 'Detail',
    read: 'Read',
  },
  action: {
    search: 'Search',
    create: 'Create',
    add: 'Add',
    del: 'Delete',
    delete: 'Delete',
    edit: 'Edit',
    update: 'Update',
    reset: 'Reset',
    preview: 'Preview',
    more: 'More',
    sync: 'Sync',
    save: 'Save',
    detail: 'Detail',
    export: 'Export',
    import: 'Import',
    userImport: 'User Import',
    generate: 'Generate',
    logout: 'Force Logout',
    test: 'Test',
    typeCreate: 'Create Dictionary Type',
    typeUpdate: 'Edit Dictionary Type',
    dataCreate: 'Create Dictionary Data',
    dataUpdate: 'Edit Dictionary Data',
    resetPassword: 'Reset Password',
    assignRole: 'Assign Role',
    confirm: 'Confirm',
    cancel: 'Cancel',
    drag: 'Drag files here, or',
    upload: 'Click to Upload',
    updateExistingUsers: 'Update existing user data?',
    fileFormat: 'Only xls and xlsx format files are allowed.',
    downloadTemplate: 'Download Template',
    assign: 'Assign Role',
    menuPermission: 'Menu Permission',
    dataPermission: 'Data Permission',
    expand: 'Expand/Collapse',
    refreshMenuCache: 'Refresh Menu Cache',
    refreshConfigCache: 'Refresh Cache',
    searchIcon: 'Search Icon',
    push: 'Push',
    fileUpload: 'File Upload',
    markRead: 'Mark as read',
    markAllRead: 'All read',
    putOnShelf: 'Put on shelf',
    removeOffShelf: 'Remove off shelf',
    addKeyWord: '+ New keyword',
    uploadFormat: 'xls/xlsx files with a size less than 10MB',
    moveOffShelf: 'Move off shelf',
    importingList: 'Importing List',
    assignCourse: 'Assign',
    statistics: 'Statistics',
    back: 'Back',
    offShelf: 'Off Shelf',
    onShelf: 'On Shelf',
    clickRetry: 'Click here to retry',
    chooseExamPaper: ' Choose exam paper',
    uploading: 'Uploading',
    previous: 'Previous',
    unzip: 'Unzip',
    moveUp: 'Move up',
    moveDown: 'Move down',
    uploadFile: 'Upload',
    questionManagement: 'Question Management',
    addChoice: '+ Add Choice',
    close: 'Close',
    view: 'View',
    chooseAll: 'Choose all',
    remove: 'Remove',
    reAssign: 'Reassign',
    answerDetails: 'Answer Details',
    uploadInBatch: 'Upload in Batch',
    parsingList: 'Parsing List',
    record: 'Records',
    restart: 'Restart',
    report: 'Report',
    batchDelete: 'Batch Delete',
    settingInBatch: 'Setting in Batch',
    submit: 'Submit',
    refresh: 'Refresh',
    resissue: 'Reissue',
  },
  dialog: {
    dialog: 'Dialog',
    open: 'Open',
    close: 'Close',
    menuPermission: 'Menu Permission',
    dataPermission: 'Data Permission',
    test: 'Test',
    detail: 'Detail',
    searchMenu: 'Menu Search',
    // --------我的站内信----------
    messageDetails: 'Message Details',
    import: 'Import',
    resourceSelection: 'Resource Selection',
    selectEmployees: 'Select Employees',
    selectCompany: 'Select Company',
    selectDepartment: 'Select Department',
    select: 'Select',
    edit: 'Edit',
    resourceModificationLog: 'Resource Modification Log',
    selectClassroom: 'Select Classroom',
    confirm: 'Confirm',
    venue: 'Venue',
    companyDetail: 'Company Detail'


  },
  sys: {
    api: {
      operationFailed: 'Operation failed',
      errorTip: 'Error Tip',
      errorMessage: 'The operation failed, the system is abnormal!',
      timeoutMessage: 'Login timed out, please log in again!',
      apiTimeoutMessage: 'The interface request timed out, please refresh the page and try again!',
      apiRequestFailed: 'The interface request failed, please try again later!',
      networkException: 'network anomaly',
      networkExceptionMsg:
        'Please check if your network connection is normal! The network is abnormal',

      errMsg401: 'The user does not have permission (token, user name, password error)!',
      errMsg403: 'The user is authorized, but access is forbidden!',
      errMsg404: 'Network request error, the resource was not found!',
      errMsg405: 'Network request error, request method not allowed!',
      errMsg408: 'Network request timed out!',
      errMsg500: 'Server error, please contact the administrator!',
      errMsg501: 'The network is not implemented!',
      errMsg502: 'Network Error!',
      errMsg503: 'The service is unavailable, the server is temporarily overloaded or maintained!',
      errMsg504: 'Network timeout!',
      errMsg505: 'The http version does not support the request!',
      errMsg901: 'Demo mode, no write operations are possible!'
    },
    app: {
      logoutTip: 'Reminder',
      logoutMessage: 'Confirm to exit the system?',
      menuLoading: 'Menu loading...'
    },
    exception: {
      backLogin: 'Back Login',
      backHome: 'Back Home',
      subTitle403: "Sorry, you don't have access to this page.",
      subTitle404: 'Sorry, the page you visited does not exist.',
      subTitle500: 'Sorry, the server is reporting an error.',
      noDataTitle: 'No data on the current page.',
      networkErrorTitle: 'Network Error',
      networkErrorSubTitle:
        'Sorry, Your network connection has been disconnected, please check your network!'
    },
    lock: {
      unlock: 'Click to unlock',
      alert: 'Lock screen password error',
      backToLogin: 'Back to login',
      entry: 'Enter the system',
      placeholder: 'Please enter the lock screen password or user password'
    },
    login: {
      backSignIn: 'Back sign in',
      mobileSignInFormTitle: 'Mobile sign in',
      qrSignInFormTitle: 'Qr code sign in',
      signInFormTitle: 'Sign in',
      signUpFormTitle: 'Sign up',
      forgetFormTitle: 'Reset password',

      signInTitle: 'Backstage management system',
      signInDesc: 'Enter your personal details and get started!',
      policy: 'I agree to the xxx Privacy Policy',
      scanSign: `scanning the code to complete the login`,

      loginButton: 'Sign in',
      registerButton: 'Sign up',
      rememberMe: 'Remember me',
      forgetPassword: 'Forget Password?',
      otherSignIn: 'Sign in with',

      // notify
      loginSuccessTitle: 'Login successful',
      loginSuccessDesc: 'Welcome back',

      // placeholder
      accountPlaceholder: 'Please input username',
      passwordPlaceholder: 'Please input password',
      smsPlaceholder: 'Please input sms code',
      mobilePlaceholder: 'Please input mobile',
      policyPlaceholder: 'Register after checking',
      diffPwd: 'The two passwords are inconsistent',

      userName: 'Username',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      email: 'Email',
      smsCode: 'SMS code',
      mobile: 'Mobile'
    },
    // 用户模块
    user: {
      userName: "Name",
      nickName: "Name",
      userNumber: "User Number",
      userNickname: "NickName",
      department: "Department",
      company: "Company",
      phoneNumber: "Phone",
      userNameRule: "The user name cannot be empty",
      userNameLengthRule: "The length must be between 5 and 50",
      nickNameLengthRule: "The length must be between 1 and 128",
      userNicknameRule: "The name cannot be empty",
      passwordRule: "The user password cannot be empty",
      passwordLengthRule: "The user password must be between 5 and 20 in length",
      emailRule: "Please enter the correct email address",
      phoneNumberRule: "Please enter the correct mobile number",
      deleteTip: "Are you sure you want to delete the user",
      resetPasswordTip: "Please enter the new password for",
      resetPasswordConfirm: "After reset, the employee's login password will be reset and send an email to employee Confirm or not?",
      resetPwdSuccess: "Update successful",
      import: "User import",
      importRes: "Import result",
      addUser: "Add User",
      editUser: "Edit User",
      deptNamePH: "Please input",
      userNamePH: "Please enter the name",
      nickNamePH: "Please enter the name",
      phoneNumberPH: "Please enter your phone number",
      resetPassword: "Reset Password",
      assignRoles: "Assign Roles",
      departmentPH: "Select a department",
      departmentRule: "The department cannot be empty",
      companyPH: "Select a company",
      companyRule: "The company cannot be empty",
      sectionPH: "Select section",
      sectionRule: "The section cannot be empty",
      positionPH: "Select position",
      positionRule: "The position cannot be empty",
      userNicknamePH: "Please enter a user nickname",
      email: "Email",
      emailPH: "Please enter your email",
      password: "Password",
      passwordPH: "Please enter the password",
      gender: "Gender",
      genderPH: "Select a gender",
      lineManagerPH: "Select lineManagers",
      nationality: "Nationality",
      workTerms: "Work Terms",
      nationalityPH: "Select an nationality",
      workTermPH: "Select a work terms",
      position: "Position",
      role: "Role",
      rolePH: "Select a role",
      roleRule: "The role cannot be empty",
      updateData: "Whether to update existing user data",
      importTip: "Only xls and xlsx files can be imported",
      downloadTemplate: "Download Template",
      section: "Section",
      badgePH: "Please enter badge",
      badgeNo: "BadgeNo",
      badgeNoPH: "Please enter your badge",
      badgeNoRule: "The badge cannot be empty",
      lineManager: "Line Manager",
      status: "Status",
      statusPH: "Select a status",
      statusRule: "The status cannot be empty",
      onboardingDate: "Hire Date",
      onboardingDatePH: "Select hire date",
      createTime: "Creation Time",
      userStatus: "User Status",
      userStatusRule: "The users status cannot be empty",
      userStatusPH: "Select a user status",
      workTypeRule: "The workType cannot be empty",
      workType: "Work Type",
      workTypePH: "Select a work type"
    },
    // 公司模块
    company: {
      addCompany: 'Add Company',
      editCompany: 'Edit Company',
      companyName: 'Company Name',
      abbreviation: 'Abbreviation',
      type: 'Contractor',
      uniqueCode: 'Unique Code',
      dataSource: 'Data Source',
      superiorCompany: 'Superior Company',
      sort: 'Sort',
      createTime: 'Create Time',
      creator: 'Creator',
      status: 'Status',
      yes: 'Yes',
      no: 'No',
      orderNum: 'Company Sort',
      companyCode: 'Company Code',
      companyNamePH: "Please input the company name",
      typePH: "Please select the contractor",
      uniqueCodePH: "Please input the unique code",
      dataSourcePH: "Please input the dataSource",
      superiorCompanyPH: "Please select the superior company",
      abbreviationPH: 'Please input the abbreviation',
      companyCodePH: 'Please input the company code',
      serviceCompanyIdRule: 'Please select superior company',
      deptNameRule: 'Please input the company name',
      shortNameRule: 'Please input the abbreviation',
      orderNumRule: 'Show the sequence can not be empty',
      deptCodeRule: 'Please input the company code',
      typeRule: 'Please select the contractor'
    },
    // 部门模块
    dept: {
      addDept: 'Add Department',
      editDept: 'Edit Department',
      expand: 'Fold/Unfold',
      totalTip: "Total number of departments",
      deptName: 'Department Name',
      superiorDept: 'Superior Department',
      superiorDeptPH: 'Please select superior department',
      deptSort: 'Department Sort',
      deptCode: 'Department Code',
      deptCodePH: 'Please input the department code',
      delTip: "Are you sure you want to delete the data with the department name of",
      refuseDelTip: "The current department has subdepartments and cannot be deleted",
      deptNamePH: "Please enter the department name",
      parentIdRule: "Please select superior department",
      deptNameRule: "Please input the department name",
      shortNameRule: "Please input the abbreviation",
      orderNumRule: 'Show the sequence can not be empty',
      deptCodeRule: 'Please input the department code',
      typeRule: 'Please select the type',
      parentPH: "Please select the superiordepartment",
      abbrPH: "Please enter the department abbreviation"
    },
    // 部分模块
    section: {
      addSection: 'Add Section',
      editSection: 'Edit Section',
      sectionName: 'Section Name',
      sectionNamePH: 'Please input the section name',
      superiorSection: 'Superior Section',
      superiorSectionRule: 'Please select superior section',
      sectionSort: 'Section Sort',
      sectionCode: 'Section Code',
      sectionCodePH: 'Please input the section code',
      sectionNameRule: 'Please input section name',
      sectionCodeRule: 'Please input section code',
      orderNumRule: 'Please input section sort',
    },
    // 岗位模块
    post: {
      addPost: 'Add Position',
      editPost: 'Edit Position',
      postName: 'Position',
      deptName: 'Automatically generated',
      section: 'Section',
      sectionPH: 'Please select section',
      parentPosition: 'Parent Position',
      parentPositionPH: 'Please select parent position',
      positionName: 'Position Name',
      positionPH: 'Please input position name',
      positionSort: 'Position Sort',
      positionCode: 'Position Code',
      positionCodePH: 'Please input the position code',
      positionNameRule: 'Please input position name',
      positionCodeRule: 'Please input the position code',
      orderNumRule: 'Please input position sort',
      totalTip: "Total number of positions"
    },
    // 配置管理(一期)
    config: {
      addConfig: 'Add Config',
      editConfig: 'Edit Config',
      configName: 'Config Name',
      configNamePH: 'Please input config name',
      configKey: 'Config Key',
      configKeyPH: 'Please input config key',
      builtInSystem: 'Built in system',
      configId: 'Config ID',
      configValue: 'Config Value',
      configValuePH: 'Please input config value',
      remark: 'Remark',
      remarkPH: 'Please input remark',
      configNameRule: 'Config name cannot be empty',
      configKeyRule: 'Config key cannot be empty',
      configValueRule: 'Config value cannot be empty',

    }
  },
  profile: {
    user: {
      title: 'Personal Information',
      username: 'User Name',
      nickname: 'Nick Name',
      mobile: 'Phone Number',
      email: 'User Mail',
      dept: 'Department',
      posts: 'Position',
      roles: 'Own Role',
      sex: 'Sex',
      man: 'Man',
      woman: 'Woman',
      createTime: 'Created Date'
    },
    info: {
      title: 'Basic Information',
      basicInfo: 'Basic Information',
      resetPwd: 'Reset Password',
      userSocial: 'Social Information'
    },
    rules: {
      nickname: 'Please Enter User Nickname',
      mail: 'Please Input The Email Address',
      truemail: 'Please Input The Correct Email Address',
      phone: 'Please Enter The Phone Number',
      truephone: 'Please Enter The Correct Phone Number'
    },
    password: {
      oldPassword: 'Old PassWord',
      newPassword: 'New Password',
      confirmPassword: 'Confirm Password',
      oldPwdMsg: 'Please Enter Old Password',
      newPwdMsg: 'Please Enter New Password',
      cfPwdMsg: 'Please Enter Confirm Password',
      diffPwd: 'The Passwords Entered Twice No Match'
    }
  },
  cropper: {
    selectImage: 'Select Image',
    uploadSuccess: 'Uploaded success!',
    modalTitle: 'Avatar upload',
    okText: 'Confirm and upload',
    btn_reset: 'Reset',
    btn_rotate_left: 'Counterclockwise rotation',
    btn_rotate_right: 'Clockwise rotation',
    btn_scale_x: 'Flip horizontal',
    btn_scale_y: 'Flip vertical',
    btn_zoom_in: 'Zoom in',
    btn_zoom_out: 'Zoom out',
    preview: 'Preivew'
  },
  input: {
    // --------Role Module----------
    roleNamePlaceholder: 'Please enter role name',
    roleKeyPlaceholder: 'Please enter role key',
    orderPlaceholder: 'Please enter display order',

    // --------Menu Module----------
    menuCodePlaceholder: 'Please enter menu code',
    menuPlaceholder: 'Please enter menu name',
    menuNamePlaceholder: 'Please enter the Chinese menu name',
    menuNameEnPlaceholder: 'Please enter the Chinese menu name',
    menuNameArPlaceholder: 'Please enter the Arabic menu name',
    routeAddressPlaceholder: 'Please enter route address',
    menuComponentPlaceholder: 'For example: system/user/index',
    menuComponentNamePlaceholder: 'For example: SystemUser',
    menuPermissionPlaceholder: 'Please enter permission key',


    // --------Dictionary Module----------
    dictNamePlaceholder: 'Please enter dictionary name',
    dictTypePlaceholder: 'Please enter dictionary type',
    dictStatusPlaceholder: 'Please enter dictionary status',
    dictLabelPlaceholder: 'Please enter dictionary label',
    dictLabelCnPlaceholder: 'Please enter the Chinese dictionary label',
    dictLabelEnPlaceholder: 'Please enter the English dictionary label',
    dictLabelArPlaceholder: 'Please enter the Arabic dictionary',
    dictNameTypePlaceholder: 'Please enter parameter name',
    dictValuePlaceholder: 'Please enter data label',
    dictKeyPlaceholder: 'Please enter dictionary key value',
    dictClassPlaceholder: 'Please enter CSS Class',
    dictRemarkPlaceholder: 'Please enter dictionary order',

    // --------Message Center----------
    // SMS Management Module (SMS Channel)
    smsSignPlaceholder: 'Please enter SMS signature',
    channelCodePlaceholder: 'Please select channel code',
    apiAccountPlaceholder: 'Please enter SMS API account',
    apiSecretPlaceholder: 'Please enter SMS API secret',
    apiCallbackPlaceholder: 'Please enter SMS send callback URL',

    // SMS Management Module (SMS Template)
    templateCodePlaceholder: 'Please enter template code',
    templateNamePlaceholder: 'Please enter template name',
    templateContentPlaceholder: 'Please enter template content',
    templateApiNoPlaceholder: 'Please enter SMS API template code',

    // Email Management Module (Email Template)
    emailInputPlaceholder: 'Please enter recipient email',

    // --------Internal Message Management----------
    // Internal Message Management ===> Template Management
    templateCode: 'Please enter template code',
    templateName: 'Please enter template name',
    senderName: 'Please enter sender name',
    userCodePlaceholder: 'Please enter user code',

    // --------Notice Announcement----------
    noticeTitlePlaceholder: 'Please enter announcement title',

    // --------Tenant List----------
    tenantNamePlaceholder: 'Please enter tenant name',
    contactName: 'Please enter contact name',
    contactPhone: 'Please enter contact phone',
    tenantQuotaPlaceholder: 'Please enter account quota',
    expireTime: 'Please select expiration time',
    domainPlaceholder: 'Please enter bound domain',

    // --------Notification Announcement----------
    packageNamePlaceholder: 'Please enter package name',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramNamePlaceholder: 'Please enter parameter name',
    paramKeyPlaceholder: 'Please enter parameter key',
    paramCategoryPlaceholder: 'Please enter parameter category',
    paramValuePlaceholder: 'Please enter parameter value',

  },
  // 下拉框内的默认文字
  select: {
    // Default text for dropdowns
    userStatus: 'User Status',
    userRole: 'Please select role information',
    belongingDept: 'Please select department',
    pleaseSelect: 'Please select',
    status: 'Please select status',
    menuStatus: 'Please select menu status',
    smsStatusPlaceholder: 'Please select enabled status',
    templateChannelCode: 'Please select SMS channel code',
    templateType: 'Please select SMS type',

    // SMS Management Module (SMS Log)
    templateChannel: 'Please select SMS channel',
    templateChannelPlaceholder: 'Please select SMS channel code',
    templateStatusPlaceholder: 'Please select send status',
    templateSendStatusPlaceholder: 'Please select receive status',
    templateTypePlaceholder: 'Please select type',
    templateReceiveNamePlaceholder: 'Please select recipient',

    pleaseSelectStatus: 'Please select enabled status',
    userTypePlaceholder: 'Please select user type',
    notifyTemplateTypePlaceholder: 'Please select template type',

    // --------Notice Announcement----------
    noticeStatusPlaceholder: 'Please select announcement status',
    noticeTypePlaceholder: 'Please select announcement type',
    tenantStatusPlaceholder: 'Please select status',
    tenantPackagePlaceholder: 'Please select tenant package',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    // 请选择系统内置
    paramTypePlaceholder: 'Please select built-in system option',

  },
  // 搜索区域内容标题
  search: {
    userName: 'User Name',
    phoneNumber: 'Phone Number',
    status: 'Status',
    createTime: 'Creation Time',
    search: 'Search',
    roleName: 'Role Name',
    roleKey: 'Role Key',
  },
  formValidate: {
    // --------User Module----------
    userName: 'User name cannot be empty',
    userNickName: 'User nickname cannot be empty',
    userPassword: 'User password cannot be empty',
    email: 'Please enter a valid email address',
    phoneNumber: 'Please enter a valid phone number',

    // --------Role Module----------
    roles: 'Please select a role',
    roleName: 'Role name cannot be empty',
    roleKey: 'Role key cannot be empty',
    showSort: 'Display order cannot be empty',
    status: 'Status cannot be empty',
    remark: 'Remarks cannot be empty',

    // --------Menu Module----------
    menuCode: 'Menu code cannot be empty',
    menuName: 'Chinese menu name cannot be empty',
    menuNameEn: 'English menu name cannot be empty',
    menuNameAr: 'Arabic menu name cannot be empty',
    menuSort: 'Menu order cannot be empty',
    menuPath: 'Route address cannot be empty',

    // --------Department Module----------
    parentDept: 'Parent department cannot be empty',
    deptName: 'Department name cannot be empty',
    deptShowSort: 'Display order cannot be empty',
    deptEmail: 'Please enter a valid email address',
    deptPhone: 'Please enter a valid phone number',

    // --------Post Module----------
    postName: 'Position name cannot be empty',
    postCode: 'Position code cannot be empty',
    postStatus: 'Position status cannot be empty',
    postRemark: 'Position content cannot be empty',

    // --------Dictionary Module----------
    dictName: 'Dictionary name cannot be empty',
    dictType: 'Dictionary type cannot be empty',
    dictLabel: 'Data label cannot be empty',
    dictLabelCn: 'Chinese data labels cannot be empty',
    dictLabelEn: 'English data labels cannot be empty',
    dictLabelAr: 'Arabic data labels cannot be empty',
    dictValue: 'Data key value cannot be empty',
    dictShowSort: 'Data sort order cannot be empty',

    // --------Message Center----------
    // SMS Management Module (SMS Channel)
    signature: 'SMS signature cannot be empty',
    channelCode: 'Channel code cannot be empty',
    enableStatus: 'Enable status cannot be empty',
    channelApiKey: 'SMS API account cannot be empty',

    // SMS Management Module (SMS Template)
    templateType: 'SMS type cannot be empty',
    templateOpenStatus: 'Open status cannot be empty',
    templateCode: 'Template code cannot be empty',
    templateName: 'Template name cannot be empty',
    templateContent: 'Template content cannot be empty',
    templateId: 'SMS API template ID cannot be empty',
    templateChannelCode: 'SMS channel code cannot be empty',
    templateMobile: 'Phone number cannot be empty',

    // Email Management Module (Email Template)
    templateEmail: 'Email cannot be empty',
    templateEmailContent: 'Template content cannot be empty',

    // --------Internal Message Management----------
    // Internal Message Management ===> Template Management
    messageType: 'Message type cannot be empty',
    sendName: 'Sender name cannot be empty',

    // --------Internal Message Management----------
    // Internal Message Management ===> Template Management
    userId: 'User ID cannot be empty',
    templateNumber: 'Template number cannot be empty',

    // --------Notice Announcement----------
    noticeTitle: 'Notice title cannot be empty',
    noticeContent: 'Notice content cannot be empty',
    noticeType: 'Notice type cannot be empty',

    // --------Tenant List----------
    tenantName: 'Tenant name cannot be empty',
    tenantPackage: 'Tenant package cannot be empty',
    contactName: 'Contact name cannot be empty',
    tenantStatus: 'Tenant status cannot be empty',
    tenantQuota: 'Account quota cannot be empty',
    expireTime: 'Expiration time cannot be empty',
    domain: 'Bound domain cannot be empty',

    // --------Tenant Package----------
    packageName: 'Package name cannot be empty',
    packageMenu: 'Associated menu ID cannot be empty',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    // 参数分类不能为空
    paramCategory: 'Parameter category cannot be empty',
    paramName: 'Parameter name cannot be empty',
    paramKey: 'Parameter key cannot be empty',
    paramValue: 'Parameter value cannot be empty',
    paramVisible: 'Visibility cannot be empty',
  },
  confirm: {
    // --------Menu Module----------
    refreshMenuCache: 'The cache will be refreshed and the browser updated! Refresh menu cache',
    sendMessage: 'Do you want to push the selected notifications?',
    isOffShelf: 'The student end cannot enter the course again if the course is off shelf.Confirm or not?',
    importCourse: 'Are you sure you want to import the course?',
    reImportCourse: 'Are you sure you want to re import the current course?',
    deImportCourse: 'Are you sure you want to delete the current course?',
    deleteChapter: 'Are you sure you want to delete the current chapter named',
    deleteOption: 'Confirm to delete the selected option？',
    deleteAssignment: 'Confirm to cancel the assignment?',
    deleteChoice: 'Delete this choice, confirm or not？',
    deleteAssignCourse: 'Confirm whether to cancel the assignment?',
    uploading: 'Uploading, the uploading will be interrupted if it is closed, still sure to close it?',
    deleteScorm: 'Sure to delete scorm with the title of " ',
    refreshSuccess: 'Refresh the current page will make the uploading be interrupted, are you sure to continue to refresh the current page?',
    sureTo: 'Sure to',
    resourceOf: 'the resource of',
    success: 'success',
    deleteResource: 'Sure to delete resource with the ID of',
  },
  success: {
    sendSuccess: 'Submission sent successfully! Send result, see send log ID:',
    pushSuccess: 'Push successful',
    // --------我的站内信----------
    readSuccess: 'All read successfully!',
    // 批量已读成功
    readSuccessBatch: 'Batch read successfully!',
  },
  descriptions: {
    // --------Message Center Module----------
    // SMS Management ---> SMS Log (Details)
    logId: 'Log Primary Key',
    channelId: 'SMS Channel',
    templateId: 'SMS Template',
    templateCode: 'API Template Code',
    userId: 'User Information',
    content: 'SMS Content',
    params: 'SMS Parameters',
    createTime: 'Creation Time',
    sendStatus: 'Send Status',
    sendTime: 'Send Time',
    sendResult: 'API Send Result',
    sendLogId: 'SMS ID',
    requestId: 'API Request ID',
    receiveStatus: 'API Receive Status',
    receiveResult: 'API Receive Result',

    // Internal Message Management ===> Message Records
    id: 'ID',
    userType: 'User Type',
    userNumber: 'User Number',
    templateNumber: 'Template Number',
    templateCodes: 'Template Code',
    senderName: 'Sender Name',
    templateContent: 'Template Content',
    templateParams: 'Template Parameters',
    templateType: 'Template Type',
    readStatus: 'Read Status',
    readTime: 'Read Time',
    // --------我的站内信----------
    sendName: 'Send Name',
    messageType: 'Message type',
    messageContent: 'Content',
  },
  global: {
    status: 'Status',
    createTime: 'Creation Time',
    deleteSuccess: 'Delete Successfully',
    importSuccess: 'Import Successfully',
    importRowLimit: 'Exceeding the limit of imported rows',
    notInFormat: 'The uploaded form does not conform to the format',
    enable: 'Enable',
    disable: 'Disable',
    changeStatusFront: 'Are you sure you want to',
    the: 'the',
    user: 'user',
    successfully: 'Successfully',
    tip: 'Tip',
    ok: 'Ok',
    cancel: 'Cancel',
    editSuccess: 'Edit Successful',
    addSuccess: 'Added Successfully',
    pleaseSelectCourse: 'Please select at least one course',
    submitSuccess: 'Submit successfully',
    startDate: 'Start Date',
    endDate: 'End Date',
    unknown: 'Unknown',
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    deleteTip: 'Are you sure you want to delete ',
    deleteFile: 'Data is uploading, to delete it or not?',
    deleteExam: 'Please note the learner cannot check the exam after deletion. Sure to delete?',
    import: 'Import',
    export: 'Export',
    action: 'Action',
    selectPH: 'Please select',
    remark: 'Remark',
    inputPH: 'Please enter content',
    confirm: 'Confirm',
    fileDrag: 'Drag and drop files here',
    or: 'or',
    clickToUpload: 'click to upload',
    select: 'Please select data',
    clearSelection: 'Clear Selection',
    company: 'Company',
    refresh: 'Refresh cache Successfully',
    video: 'Video',
    audio: 'Audio',
    file: 'File',
    scorm: 'Scorm',
    aicc: 'AICC',
  },
  hr: {
    section: {
      refuseDelTip: "Subsections exist and cannot be deleted",
      delTip: "Are you sure to delete the section named",
      des: "Description",
      totalTip: "Total number of sections"
    }
  },
  // 一期关于分类国际化
  category: {
    topic: {
      addTopic: 'Add',
      editTopic: 'Edit',
      subjectName: 'Subject',
      subjectNamePH: 'Please Input',
      keyWords: 'Key Words',
      introduction: 'Introduction',
      sort: 'Sort',
      creationTime: 'Creation Time',
      parentSubject: 'Parent Subject',
      parentSubject0: 'Parent Subject0',
      parentSubject2: 'Parent Subject2',
      cover: 'Cover',
      coverPH: 'PNG、JPG、GIF format,under 500KB',
      subjectNameRule: 'Please input subject',
      keyWordsRule: 'Please input key words',
      introductionRule: 'Please input introduction',
    },
    journey: {
      addCategory: 'Add Category',
      editCategory: 'Edit Category',
      titleRule: 'Category is required',
      sortRule: 'Sort is required',
      categoryTitle: 'Category',
      categoryTitlePH: 'Please Input',
      title: 'Title',
      sort: 'Sort',
      creationTime: 'Creation Time',
      creator: 'Creator'
    }
  },
  // 操作日志
  log: {
    operaLog: {
      logId: 'Log ID',
      operator: 'Operator',
      operationModule: 'Operation Module',
      operationName: 'Operation Name',
      operationContent: 'Operation Content',
      operationTime: 'Operation Time',
      businessNo: 'Business No',
      actionIP: 'Operation IP',
      actionLog: 'Operation Log.xls',
      traceId: 'Trace ID',
      operatorId: 'Operator ID',
      operatorName: 'Operator Name',
      operatorUA: 'Operator UA',
      operatorExt: 'Operator Extension Parameters',
      requestURL: 'Request URL',
      operatorPH: 'Please enter operator',
      operatorModulePH: 'Please enter operation module',
      operatorNamePH: 'Please enter operator name',
      operatorContentPH: 'Please enter operation content',
      businessNoPH: 'Please enter business number',
    },
    loginLog: {
      userName: 'User Name',
      loginAddress: 'Login Address',
      loginTime: 'Login Time',
      logId: 'Log ID',
      operationType: 'Operation Type',
      browser: 'Browser',
      loginResult: 'Login Result',
      actionLog: 'Login Log.xls',
      userNamePH: 'Please enter user name',
      loginAddressPH: 'Please enter login address',
    }
  },
  // learning-center
  learningCenter: {
    course: {
      days: 'Days',
      employee: 'Employee',
      course: 'Course',
      courses: 'Courses',
      id: 'ID',
      format: 'Format',
      courseTemplate: 'Course Template.xlsx',
      title: 'Title',
      onShelfStatus: 'On Shelf Status',
      newCourse: 'New Course',
      isRecommend: 'Recommendation',
      isAssigned: 'Assigned Status',
      level: 'Level',
      language: 'Language',
      duration: 'Duration',
      subTitle: 'Subtitle',
      courseSource: 'Course Source',
      uniqueId: 'Unique ID',
      tasks: 'Tasks',
      exam: 'Exam',
      exams: 'Exams',
      assignedNumber: 'Assigned Number',
      basicInfo: 'Basic Information',
      courseCatalogue: 'Course Catalogue',
      assignScope: 'Assign Scope',
      courseStatistics: 'Course Statistics',
      addCourse: 'Add Course',
      topicIdRule: 'Please select',
      courseNameRule: 'Please input the course,no more than 200 char',
      courseName: 'Course name',
      coverPH: '750*442 pixels or 16:9,PNG、JPG、GIF format,under 5M',
      timeliness: 'Timeliness',
      permanent: 'Permanent',
      setExpirationDate: 'Set expiration date',
      certificate: 'Certificate',
      autoAssign: 'Auto Assign',
      courseDescription: 'Course Description',
      taskMgt: 'Task Management',
      studentName: 'Name',
      badgeNo: 'Badge No.',
      email: 'Email',
      type: 'Type',
      deptName: 'Department',
      section: 'Section',
      company: 'Company',
      position: 'Position',
      star: 'Star',
      operator: 'Operator',
      sendingStatus: 'Sending Status',
      score: 'Score',
      status: 'Status',
      electiveCourse: 'Students for elective course',
      mandatoryCourse: 'Mandatory courses completed',
      comprehensiveCourse: 'Comprehensive course grading',
      selectCourseRule: 'Please select course information',
      chooseCourse: 'Choose Courses',
      taskId: 'Task ID',
      contentTitle: 'Content Title',
      contentId: 'Content ID',
      assetUUID: 'Asset UUID',
      categoryL1: 'Category-L1',
      areaL2: 'Area-L2',
      subjectL3: 'Subject-L3',
      channelL4: 'Channel-L4',
      estimatedDuration: 'Estimated Duration',
      isExam: 'Exam(Yes/No)',
      isSubtitle: 'Subtitle(Y/N)',
      isLocal: 'Local/Cloud',
      imageUrl: 'Image Url',
      keywords: 'Keywords',
      courseFileName: 'Course File Name',
      fileLocation: 'File Location',
      importTime: 'Import Time',
      mandatoryScope: 'Mandatory Scope',
      languageRule: 'Language cannot be null',
      titleRule: 'The title cannot be empty',
      durationRule: 'The duration cannot be empty',
      setTeachingContent: 'Set Teaching Content',
    },
    exam: {
      examPaper: 'Exam paper',
      addExam: 'Add Exam',
      editExam: 'Edit Exam',
      customizedPaper: 'Customized Paper',
      autoPaper: 'Auto Paper',
      passScore: 'Pass Score',
      passScorePH: 'Please input exam score',
      points: 'points',
      minutes: 'minutes',
      examScorePH: 'Please input exam score',
      editExamTip: 'Editing the exam does not affect the scores and results of the students who participated in the exam previously, but only the data after editing.',
      examPaperName: 'Exam Paper Name',
      examDuration: 'Exam Duration',
      examAttempts: 'Exam Attempts',
      taskName: 'Task Name',
      appendix: 'Appendix',
      examTimes: 'Exam Times',
      passFail: 'Pass/Fail',

    },
    paper: {
      paperName: 'Paper Name',
      type: 'Type',
      itemNumber: 'Item number',
      fullScore: 'Full score',
    },
    task: {
      addTask: 'Add Task',
      tasks: 'Tasks',
      totalTasks: 'Total Tasks',
      taskNo: 'Task No.',
      taskName: 'Task Name',
      type: 'Type',
      progress: 'Progress',
    },
    journey: {
      addJourney: 'Add Journey',
      editJourney: 'Edit Journey',
      title: 'Title',
      cover: 'Cover',
      categoryTitle: 'Category',
      uniqueId: 'Unique ID',
      creationTime: 'Creation Time',
      journeyTitleRule: 'Please input the journey,no more than 200 char',


    },
    boarding: {
      addBoarding: 'Add Onboarding',
      editBoarding: 'Edit Onboarding',
      boarding: 'Onboarding',
      category: 'Category',
      mandatoryRule: 'Mandatory is required',
      titleRule: 'Title is required',
      coverRule: 'Cover is required',
      fileRule: 'File is required',
      durationRule: 'Duration is required',
      mandatory: 'Mandatory',
      description: 'Description',
      name: 'Name',
      badgeNumber: 'Badge Number',
      title: 'Title',
      learningRecords: 'Learning Records',
    },
    companyPolicy: {
      companyPolicy: 'Company Policy',
      ackRule: 'Acknowledge is required',
      declarationRule: 'Declaration is required',
      titleRule: 'Title is required',
      categoryIdRule: 'Category is required',
      coverRule: 'Cover is required',
      muploaderRule: 'File is required',
      durationRule: 'Duration is required',
      addCompanyPolicy: 'Add Company Policy',
      editCompanyPolicy: 'Edit Company Policy',
      ack: 'Acknowledge',
      controlledScope: 'Controlled Scope',
      acknowledgement: 'Acknowledgement',
    },
    orientation: {
      displayRule: 'Display is required',
      addOrientation: 'Add Orientation',
      editOrientation: 'Edit Orientation',
      display: 'Display',
      orientation: 'Orientation',
    }
  },
  // 考试
  examMgt: {
    question: {
      poolTitle: 'Pool title',
      contentRule: 'Please input the content',
      questionContent: 'Question content',
      questionType: 'Question type',
      choice: 'Choice',
      correct: 'Correct',
      itemTitleRule: 'Please input item title',
      choiceSubjectRule: 'Please choice subject',
      itemTitle: 'Item title',
      correctAnswer: 'Correct answer:',
      questionAnalysis: 'Question analysis',
      addNewQuestion: 'Add new question',
      editQuestion: 'Edite question',
      questionPreview: 'Question Preview',
      singleChoice: 'Single choice',
      multipleChoice: 'Multiple choice',
      trueOrFalse: 'True or false',
      thisQuestion: 'this question?',
    },
    paper: {
      name: 'Name',
      itemName: 'Item name',
      questionNumber: 'Question number',
      content: 'Content',
      type: 'Type',
      selectedQuestion: 'Selected question:',
      setScoreInBatch: 'Set score in batch',
      paperNameRule: 'Please input the paper name',
      createAutoPaper: 'Create Auto Paper',
      namePH: 'Please input the name',
      chooseQuestion: 'Choose Question',
      questionPackage: 'Question Package',
      multipleChoiceType: 'Multiple choice type',
      fullScore: 'Full score:',
      chooseQuestionPackage: 'Choose question package',
      createCustomizedPaper: 'Create Customized Paper',
      editAutoPaper: 'Edite Auto Paper',
      editCustomizedPaper: 'Edite Customized Paper',
      pleaseChooseTheQuestion: 'Please choose the question, the questions are same for staff',
      autoPaper: 'Auto-Paper',
      autoPaperPH: 'Pre-set choosing from one or several question banks. When start examing, system will randomly choose from the question package',
    },
    exam: {
      notAttend: 'Not attend',
      failed: 'Failed',
      fail: 'Fail',
      pass: 'Pass',
      numberExam: 'Number of exam participants',
      numberPass: 'Number of passed employees',
      numberFailed: 'Number of failed employees',
      examPassRate: 'Exam pass rates',
      onSchedule: 'On Schedule',
      submitTime: 'Submit Time',
      topScore: 'Top score',
      examNameRule: 'Please input exam name',
      examTimeRule: 'Please choose exam time',
      examAttemptsRule: 'Please input exam attempts',
      passScoreRule: 'Please input pass score',
      examDurationRule: 'Please input exam duration',
      examPaperRule: 'Please choose exam paper',
      examName: 'Exam Name',
      examTime: 'Exam Time',
      selectExamPaper: 'Select exam paper',
      times: 'times',
      examDescription: 'Exam Description',
      examDescriptionPH: 'Please input exam description',
      examTaken: 'Examinations taken:',
      remain: 'Remain:',
      namePH: 'Please enter name',
      paperRule: 'Please choose paper',
      to: 'to',
      moment: 'moment',
      examType: 'Exam Type',
      notStarted: 'Not started',
      inProcess: 'In process',
      expired: 'Expired',
      examStatistics: 'Exam Statistics',
      score: 'Score:',
      nonAnswered: 'Non-answered',
      userList: 'User List',
    }
  },
  // 统计
  statistics: {
    course: {
      requiredUsers: 'Required Users',
      completedUsersOfRequired: 'Completed Users of Required',
      uncompletedUsersOfRequired: 'Uncompleted Users of Required',
      electiveUsers: 'Elective Users',
      subjectCourseStatistics: 'Subject Course Statistics',
      completed: 'Completed',
      sending: 'Sending',
      sendSuccess: 'SendSuccess',
      sendFail: 'SendFail',
      none: 'None',
      scorm: 'SCORM',
    },
    exam: {
      totalExams: 'Total Exams',
      passRates: 'Pass Rates',
      scoreDetails: 'Score Details',
    },
    companyPolicy: {
      allCompanyPolicy: 'All Company Policy',
      acknowledgement: 'Acknowledgement',
      optional: 'Optional',
    },
    student: {
      student: 'Student',
      active: 'Active',
      inactive: 'Inactive',
      badge: 'Badge',
      mandatoryCourse: 'Mandatory Course',
      electiveCourse: 'Elective Course',
      exam: 'Exam',
      courseEvaluation: 'Course Evaluation',
    },
    training: {
      // 统计卡片
      totalCourses: 'Total Courses',
      offlineClasses: 'Offline Classes',
      virtualClasses: 'Virtual Classes',
      hybridClasses: 'Hybrid Classes',
      totalStudents: 'Total Students',
      hstDdtCourses: 'HST/DDT Courses',

      // 报告和图表
      report: 'Report',
      exportReport: 'Export Report',

      // 表格列标题
      courseTitle: 'Course Title',
      courseCode: 'Course Code',
      category: 'Category',
      noOfClasses: 'No.of classes',
      plannedAttendance: 'Planned attendance',
      actualAttendance: 'Actual attendance',
      noShow: 'No Show',
      attendance: 'Attendance',
      classEvaluationScore: 'Class Evaluation Score',
      trainerEvaluationScore: 'Trainer Evaluation Score',
      facilityEvaluationScore: 'Facility Evaluation Score',

      // 图表标题
      percentageActualAttendanceNoShow: 'Percentage of Actual attendance And No Show',
      passportIssued: 'Passport Issued',
      bocAosKbr: 'BOC-AOS-KBR',
      noShowReport: 'No Show Report',
      coursesDelivered: 'Courses Delivered',
      noOfCourses: 'No.of courses',
      weeklyReport: 'Weekly Report',
      mlcDdtWeeklyReport: 'MLC-DDT Weekly Report',

      // 详情页面
      student: 'Student',
      class: 'Class',
      company: 'Company',
      trainer: 'Trainer',
      details: 'Details',

      // 统计数据
      total: 'Total',
      plannedAttendanceLabel: 'Planned attendance',
      actualAttendanceLabel: 'Actual attendance',
      noShowLabel: 'No Show',

      // 培训师相关
      trainerType: 'Trainer Type',
      trainerName: 'Trainer Name',
      trainerDetail: 'Trainer Detail',

      // 课堂相关
      classCode: 'Class Code',
      classType: 'Class Type',
      classroom: 'Classroom',
      duration: 'Duration',

      // 时间选项
      thisWeek: 'This week',
      thisMonth: 'This month',
      thisYear: 'This year',
      time: 'Time',

      // 公司相关
      studentNumber: 'Student Number',
      completionRate: 'Completion Rate',
      notCompleted: 'Not Completed',

      // 学生相关
      testResult: 'Test Result',
      attendanceStatus: 'Attendance Status',
      name: 'Name',
      badgeNumber: 'Badge Number',
      position: 'Position',

      // 课堂详情相关
      classDetail: 'Class Detail',
      courseName: 'Course Name',
      checkIn: 'Check-in',
      checkOut: 'Check-out',
      checkInOut: 'Check-in/out',
      department: 'Department',
      badgeNo: 'Badge No.',

      // 公司详情相关
      companyDetail: 'Company Detail',
      companyName: 'Company Name'
    }
  },
  dashboard: {
    quick: {
      quickEntrance: 'Quick Entrance',
    },
    statistics: {
      totalStudents: 'Total Students',
      distinctStudents: 'Distinct Students',
      totalAdmins: 'Total Admins',
      distinctAdmins: 'Distinct Admins',
      loginStatistics: 'Login Statistics',
    },
    total: {
      totalNumberOfCourse: 'Total Number of Course',
      totalNumberOfExam: 'Total Number of Exam',
      totalNumberOfOnboarding: 'Total Number of Onboarding',
      totalNumberOfOrientation: 'Total Number of Orientation',
      totalNumberOfCompanyPolicy: 'Total Number of Company Policy',
      user: 'User'
    }
  },
  // 资源管理
  resource: {
    id: 'ID',
    size: 'Size',
    referenced: 'Referenced',
    dataPermissionsRule: 'Data permissions cannot be empty',
    fileUploadCompleted: 'File upload completed',
    thereAreStillUnprocessedFiles: 'There are still unprocessed files',
    allFilesError: 'All files are fully uploaded, but there is an error/errors',
    supportResource: 'Support uploading up to 5 resources at a time. Format supports: Zip, MP3, MP4, PPT, DOCX, PDF, Excel, TXT.',
    eventType: 'Event Type',
    before: 'Before',
    resourceCreated: 'Resource Created',
    after: 'After',
    operateTime: 'OperateTime',
    noResourceModificationRecord: 'There is no resource modification record by now',
    languageRule: 'The language cannot be null',
    uploadIn: 'Uploading in',
    batch: 'Batch',
    waitingForUploads: 'Waiting for uploads...',
    // TO DO
    uploadFailed: 'Upload Failed',
    fileBeingAnalyzed: 'The file is being analyzed, which may take some time',
    fileParsing: 'The file is being parsed...',

  },
  warning: {
    noResource: 'There are currently selected courses with no resources, please check',
    noResourceList: 'The current course does not have resources and is prohibited from listing',
    unsupportedCourseFileTypes: 'Unsupported course file types, please check',
    canNotDelete: 'Can\'t delete',
    noResourcesByNow: 'No resources need to be submitted by now',
    resourcesUploadedIncorrectly: 'There are resources that have been uploaded incorrectly. Please check',
    someFilesNotUploaded: 'Some files have not been uploaded',
    noPreview: 'The current file type does not support preview',
    delOption: 'Please delete the unfilled options',
    delContent: 'Please fill in the question content',
    answerOption: 'Please select the correct answer',
  },

  survey: {
    // Common
    category: 'Category',
    template: 'Template',
    question: 'Question',
    instance: 'Instance',
    response: 'Response',
    statistics: 'Statistics',

    // Category Management
    categoryManagement: 'Category Management',
    categoryName: 'Category Name',
    parentCategory: 'Parent Category',
    rootCategory: 'Root Category',
    sortOrder: 'Sort Order',
    status: 'Status',
    enabled: 'Enabled',
    disabled: 'Disabled',
    description: 'Description',
    addCategory: 'Add Category',
    editCategory: 'Edit Category',
    addSubcategory: 'Add Subcategory',
    deleteCategory: 'Delete Category',
    expandAll: 'Expand All',
    collapseAll: 'Collapse All',

    // Template Management
    templateManagement: 'Template Management',
    templateName: 'Template Name',
    templateCode: 'Template Code',
    templateEditor: 'Template Editor',
    addTemplate: 'Add Template',
    editTemplate: 'Edit Template',
    copyTemplate: 'Copy Template',
    deleteTemplate: 'Delete Template',
    previewTemplate: 'Preview Template',
    publishTemplate: 'Publish Template',
    archiveTemplate: 'Archive Template',
    templateStatus: 'Template Status',
    draft: 'Draft',
    published: 'Published',
    archived: 'Archived',
    unpublished: 'Unpublished',
    ongoing: 'Ongoing',
    ended: 'Ended',
    questionCount: 'Question Count',

    // Question Management
    questionManagement: 'Question Management',
    questionTitle: 'Question Title',
    questionType: 'Question Type',
    questionEditor: 'Question Editor',
    addQuestion: 'Add Question',
    editQuestion: 'Edit Question',
    copyQuestion: 'Copy Question',
    deleteQuestion: 'Delete Question',
    required: 'Required',
    optional: 'Optional',
    singleChoice: 'Single Choice',
    multipleChoice: 'Multiple Choice',
    trueFalse: 'True/False',
    rating: 'Rating',
    fileUpload: 'File Upload',
    text: 'Text',
    singleChoiceDesc: 'Single choice question, users can only select one option',
    multipleChoiceDesc: 'Multiple choice question, users can select multiple options',
    trueFalseDesc: 'True/False question, users choose correct or incorrect',
    ratingDesc: 'Rating question, users give a rating score',
    fileUploadDesc: 'File upload question, users upload files',
    textDesc: 'Text question, users input text content',

    // Instance Management
    instanceManagement: 'Instance Management',
    instanceName: 'Instance Name',
    instanceEditor: 'Instance Editor',
    addInstance: 'Add Instance',
    editInstance: 'Edit Instance',
    copyInstance: 'Copy Instance',
    deleteInstance: 'Delete Instance',
    publishInstance: 'Publish Instance',
    stopInstance: 'Stop Instance',
    instanceStatus: 'Instance Status',
    stopped: 'Stopped',
    expired: 'Expired',
    startTime: 'Start Time',
    endTime: 'End Time',
    department: 'Department',
    anonymous: 'Anonymous',
    scopeType: 'Scope Type',
    public: 'Public',
    internal: 'Internal',
    submissionFrequency: 'Submission Frequency',
    once: 'Once',
    onceOnly: 'Once Only',
    multiple: 'Multiple',
    unlimited: 'Unlimited',
    maxSubmissions: 'Max Submissions',
    maxResponses: 'Max Responses',
    maxResponsesTip: 'The survey will automatically end when the specified number of valid responses is received',
    submissionInterval: 'Submission Interval (Hours)',
    allowViewStatistics: 'Allow View Statistics',
    estimatedTime: 'Estimated Time (Minutes)',
    responseCount: 'Response Count',
    completionRate: 'Completion Rate',
    view: 'View',
    completed: 'Completed',
    inProgress: 'In Progress',
    notStarted: 'Not Started',
    confirmPublish: 'Confirm to publish this survey instance?',
    confirmStop: 'Confirm to stop this survey instance?',
    confirmCopy: 'Confirm to copy this survey instance?',
    confirmDelete: 'Confirm to delete this survey instance?',
    publishSuccess: 'Published successfully',
    stopSuccess: 'Stopped successfully',
    copySuccess: 'Copied successfully',
    deleteSuccess: 'Deleted successfully',
    exportSuccess: 'Exported successfully',
    instanceDetail: 'Instance Detail',
    basicInfo: 'Basic Information',
    totalResponses: 'Total Responses',
    averageTime: 'Average Time',
    todayResponses: 'Today Responses',
    minutes: 'Minutes',
    scopeConfiguration: 'Scope Configuration',
    targetType: 'Target Type',
    targetName: 'Target Name',
    user: 'User',
    role: 'Role',
    templateInfo: 'Template Information',
    questionList: 'Question List',
    trueOrFalse: 'True or False',
    unknown: 'Unknown',
    addScope: 'Add Scope',
    noScopeConfigured: 'No scope configured',
    target: 'Target',
    pleaseSelectTarget: 'Please select target',
    scopeAlreadyExists: 'Scope already exists',
    scopeConfigSuccess: 'Scope configuration successful',
    scopeAddedSuccess: 'Scope added successfully, {count} items added',
    scopeConfig: 'Scope Configuration',
    noScopeData: 'No scope data',
    availableUsers: 'Available Users',
    availableDepts: 'Available Departments',
    selectedScopes: 'Selected Scopes',
    searchUserName: 'Please enter username',
    searchMobile: 'Please enter mobile',
    searchDeptName: 'Please enter department name',
    totalDepts: 'Total {count} departments',
    scopeRemovedSuccess: 'Successfully removed {count} scopes',
    selectionError: 'Selection operation failed, please try again',
    loadUserDataError: 'Failed to load user data, please try again',
    loadDeptDataError: 'Failed to load department data, please try again',
    hours: 'Hours',
    loadFailed: 'Load failed',
    createSuccess: 'Created successfully',
    updateSuccess: 'Updated successfully',
    pleaseSelectTemplate: 'Please select a template first',
    pleaseCheckFormData: 'Please check form data',
    featureNotImplemented: 'Feature not implemented yet',
    publicSurveyNoScope: 'Public surveys do not require scope configuration, all users can participate',
    questionStatistics: 'Question Statistics',
    responseData: 'Response Data',
    totalAnswers: 'Total Answers',
    validResponses: 'Valid Responses',
    pleaseEnterUserName: 'Please enter user name',
    to: 'to',
    displayRange: 'Display Range',
    pleaseSelect: 'Please select',
    allRecords: 'All Records',
    validRecordsOnly: 'Valid Records Only',
    search: 'Search',
    reset: 'Reset',
    userInfo: 'User Info',
    completionTime: 'Completion Time',
    seconds: 'seconds',
    score: 'Score',
    valid: 'Valid',
    invalid: 'Invalid',
    submitCount: 'Submit Count',
    submitCountFormat: '{count} time(s)',
    ipAddress: 'IP Address',
    operation: 'Operation',
    viewDetail: 'View Detail',
    loadDataFailed: 'Failed to load data',
    responseDetailTitle: 'Response Detail',
    deptName: 'Department',
    totalScore: 'Total Score',
    isValid: 'Is Valid',
    validResponse: 'Valid Response',
    invalidResponse: 'Invalid Response',
    responseStatistics: 'Response Statistics',
    totalQuestions: 'Total Questions',
    answeredQuestions: 'Answered Questions',
    skippedQuestions: 'Skipped Questions',
    answerDetails: 'Answer Details',
    answer: 'Answer',
    file: 'File',
    viewFile: 'View File',
    notAnswered: 'Not Answered',
    close: 'Close',
    loadDetailFailed: 'Failed to load detail',
    totalRecords: 'Total {count} records',
    noData: 'No data',
    answered: 'Answered',
    noFileUploaded: 'No file uploaded',
    pleaseEnter: 'Please enter',
    loadStatistics: 'Load Statistics',
    loadStatisticsFailed: 'Failed to load statistics',
    loadingTemplateData: 'Loading template data...',
    points: 'pts',

    // Statistics Page
    surveyStatistics: 'Survey Statistics',
    loadingStatistics: 'Loading statistics data...',
    refreshData: 'Refresh Data',
    exportReport: 'Export Report',
    participationStatus: 'Participation Status',
    validAnswers: 'Valid Answers',
    averageScore: 'Average Score',
    highest: 'Highest',
    lowest: 'Lowest',
    averageCompletionTime: 'Average Completion Time',
    statisticsTime: 'Statistics Time',
    lastUpdated: 'Last Updated',
    submissionTimeDistribution: 'Submission Time Distribution',
    byDay: 'By Day',
    byWeek: 'By Week',
    byMonth: 'By Month',
    departmentParticipationDistribution: 'Department Participation Distribution',
    questionDetailedStatistics: 'Question Detailed Statistics',
    searchQuestions: 'Search questions...',
    clear: 'Clear',
    noMatchingQuestions: 'No matching questions found',
    noStatisticsData: 'No statistics data',
    possibleReasons: 'Possible reasons:',
    noParticipants: 'No one has participated in the survey yet',
    dataGenerating: 'Data is being generated',
    reload: 'Reload',
    dataRefreshSuccess: 'Data refreshed successfully',
    dataRefreshFailed: 'Data refresh failed',
    exportFeatureInDevelopment: 'Export feature in development...',
    pleaseEnterSearchKeyword: 'Please enter search keyword',
    foundMatchingQuestions: 'Found {count} matching questions',
    searchConditionsCleared: 'Search conditions cleared',
    dailySubmission: 'Daily Submission',
    cumulativeSubmission: 'Cumulative Submission',
    participationRate: 'Participation Rate',
    participantCount: 'Participant Count',

    // Question Statistics
    responseRate: 'Response Rate',
    people: 'People',
    noOptionData: 'No option data',
    yes: 'Yes',
    no: 'No',
    star: 'Star',
    answerCount: 'Answer Count',
    answerList: 'Answer List',
    collapse: 'Collapse',
    viewAllAnswers: 'View all {count} answers',
    uploadedFiles: 'Uploaded Files',
    uploadedPeople: 'Uploaded People',
    notUploadedCount: 'Not Uploaded Count',
    fileList: 'File List',
    fileName: 'File Name',
    uploadTime: 'Upload Time',
    fileSize: 'File Size',
    download: 'Download',
    skipCount: 'Skip Count',
    optionDistribution: 'Option Distribution',
    selectTemplate: 'Select Template',
    changeTemplate: 'Change Template',
    searchTemplateName: 'Search template name',
    templateSelectedSuccess: 'Template selected successfully',
    select: 'Select',
    selectTargetType: 'Select target type',
    searchRoleName: 'Search role name',
    pleaseSearch: 'Please enter search content',
    userName: 'User Name',
    roleName: 'Role Name',
    name: 'Name',
    statisticsOverview: 'Statistics Overview',
    loadTemplateFailed: 'Failed to load template list',
    templateSelectFailed: 'Template selection failed',
    pleaseSelectScope: 'Please select scope',
    loadScopeListFailed: 'Failed to load scope list',
    allScopeAlreadyExists: 'All selected scopes already exist',
    normal: 'Normal',
    instanceContent: 'Instance Content',
    basicInfoDesc: 'Configure basic information',
    templateInfoDesc: 'View template details',
    scopeConfigDesc: 'Configure scope',
    statisticsDesc: 'View statistics',
    timeRange: 'Time Range',
    submitFrequency: 'Submit Frequency',
    searchNickname: 'Search Nickname',
    searchEmail: 'Search Email',
    searchLeader: 'Search Leader',
    searchRoleCode: 'Search Role Code',
    nickname: 'Nickname',
    email: 'Email',
    mobile: 'Mobile',
    deptCode: 'Dept Code',
    parentDept: 'Parent Dept',
    sort: 'Sort',
    leader: 'Leader',
    phone: 'Phone',
    roleCode: 'Role Code',
    roleType: 'Role Type',
    system: 'System',
    custom: 'Custom',

    // Response Management
    responseManagement: 'Response Management',
    responseDetail: 'Response Detail',
    deleteResponse: 'Delete Response',
    exportResponse: 'Export Response',

    // Statistics Analysis
    statisticsAnalysis: 'Statistics Analysis',
    statisticsDetail: 'Statistics Detail',

    // Actions
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    copy: 'Copy',
    preview: 'Preview',
    export: 'Export',
    import: 'Import',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',

    // Messages
    importSuccess: 'Imported successfully',

    // Form Validation
    categoryNameRequired: 'Category name is required',
    templateNameRequired: 'Template name is required',
    questionTitleRequired: 'Question title is required',

    // Others
    createTime: 'Create Time',
    updateTime: 'Update Time',
    actions: 'Actions',
    loading: 'Loading...',
    refreshCategories: 'Refresh Categories',
    searchCategoryName: 'Search category name',
    autoGenerated: 'Auto-generated if empty',
    dragOrClickToAdd: 'Drag or click to add questions',
    dragQuestionHere: 'Drag questions here',
    dragQuestionDesc: 'Drag question types from the left panel or click to add',
    addOption: 'Add Option',
    allTemplates: 'All Templates',
    option: 'Option',
    selectionRange: 'Selection Range',
    correct: 'Correct',
    incorrect: 'Incorrect',
    poor: 'Poor',
    excellent: 'Excellent',
    scorePerPoint: 'Score Per Point',
    allowedTypes: 'Allowed Types',
    maxFiles: 'Max Files',
    maxSize: 'Max Size',
    lengthRequirement: 'Length Requirement',
    saveTemplateFirst: 'Please save template first',
    previewOpened: 'Preview opened',
    selectFile: 'Select File',
    insertHere: 'Insert Here',
    questionConfig: 'Question Configuration',
    fullscreen: 'Fullscreen Edit',
    exitFullscreen: 'Exit Fullscreen',
    options: 'Options',
    new: 'New ',
    scrollToSeeMore: 'Scroll to see more'
  },

  // -------------------------在线学院 ----------------------------------
  academy: {
    classroom: {
      name: 'Name',
      location: 'Location',
      roomNumber: 'Room Number',
      totalSeats: 'Total Seats',
      description: 'Description',
      status: 'Status',
      time: 'Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      pleaseSelect: 'Please select',
      pleaseInput: 'Please input',
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      edit: 'Edit',
      view: 'View',
      delete: 'Delete',
      action: 'Action',
      classroomNumber: 'Classroom Number',
      pleaseEnterName: 'Please enter the name',
      cover: 'Cover',
      default: 'Default',
      customize: 'Customize',
      pleaseInputDescription: 'Please input the description',
      cancel: 'Cancel',
      save: 'Save',
      nameRequired: 'Classroom name cannot be empty',
      roomNumberRequired: 'Classroom number cannot be empty',
      statusRequired: 'The status cannot be empty',
      locationRequired: 'Position cannot be empty',
      totalSeatsRequired: 'Classroom capacity cannot be empty'
    },
    trainer: {
      trainerType: 'Trainer Type',
      company: 'Company',
      department: 'Department',
      position: 'Position',
      time: 'Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      trainerName: 'Trainer Name',
      pleaseSelect: 'Please select',
      pleaseInput: 'Please input',
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      action: 'Action',
      badgeNo: 'Badge No.',
      // Form placeholders
      trainerNamePlaceholder: 'Please enter the trainer name',
      trainerTypePlaceholder: 'Please select trainer type',
      // Form validation messages
      trainerNameRequired: 'Trainer name cannot be empty',
      trainerTypeRequired: 'Trainer type cannot be empty',
      pleaseSelectUser: 'Please select a user',
      // Button labels
      cancel: 'Cancel',
      save: 'Save',
      // Description labels
      badge: 'Badge',
      email: 'Email'
    },
    course: {
      // Search form labels
      category: 'Category',
      categoryPlaceholder: 'Please select category',
      language: 'Language',
      languagePlaceholder: 'Please select language',
      trainerType: 'Trainer Type',
      trainerTypePlaceholder: 'Please select trainer type',
      courseTitle: 'Course Title',
      courseTitlePlaceholder: 'Please input course title',
      // Button labels
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      assign: 'Assign',
      edit: 'Edit',
      copy: 'Copy',
      delete: 'Delete',
      cancel: 'Cancel',
      confirm: 'Confirm',
      // Table column labels
      courseCode: 'Course Code',
      approvals: 'Approvals',
      validity: 'Validity',
      action: 'Action',
      months: 'Months',
      yes: 'Yes',
      no: 'No',
      // Dialog titles
      newCourseTitle: 'New Course Title',
      selectStudents: 'Select Students',
      courseCategory: 'Course Category',
      manage: 'Manage',
      remove: 'Remove',
      // Form validation
      courseNameRequired: 'Course name cannot be empty',
      courseCodeRequired: 'Course code cannot be empty',
      categoryRequired: 'Course category cannot be empty',
      coverRequired: 'Please upload a cover image',
      languageRequired: 'Language cannot be empty',
      trainerTypeRequired: 'Trainer type cannot be empty',
      approvalSettingRequired: 'Approval setting cannot be empty',
      validityRequired: 'Certificate validity period cannot be empty',
      certificateRequired: 'Certificate cannot be empty',
      bookingTimeRequired: 'Booking time setting cannot be empty',
      restrictionRequired: 'Absent time and freeze time cannot be empty',
      // Common labels
      pleaseSelect: 'Please select',
      pleaseInput: 'Please input',
      // CourseForm specific labels
      addCategory: 'Add Category',
      cover: 'Cover',
      default: 'Default',
      customize: 'Customize',
      level: 'Level',
      ifmsApproval: 'IFMS Approval',
      contractorApproval: 'Contractor Approval',
      certificate: 'Certificate',
      exam: 'Exam',
      prerequisite: 'Prerequisite',
      course: 'Course',
      attachment: 'Attachment',
      new: 'New',
      restriction: 'Restriction',
      absent: 'Absent',
      absentTime: 'times',
      unableToSubscribe: ', unable to subscribe within',
      feedback: 'Feedback',
      bookingTimeSetting: 'Booking Time Setting',
      bookingTimeDesc: '24 hours booking before the start of the Schedule',
      description: 'Description',
      // Placeholders
      pleaseSelectCertificate: 'Please select the certificate',
      pleaseSelectExam: 'Please select the Exam',
      pleaseSelectCourse: 'Please select the Course',
      // Validity
      monthsUnit: 'Month(s)',
      noRestrictions: '0 represents no restrictions',
      // Dialog titles
      template: 'Template',
      // Approval workflow
      student: 'Student',
      review: 'Review',
      end: 'End',
      // Approval descriptions
      noReviewNeeded: 'No review needed',
      submitBookingRequest: 'Submit Booking Request',
      trainingAdmin: 'Training Admin',
      lineManager: 'Line Manager',
      eptwAdmin: 'EPTW Admin',
      contractorHolder: 'Contractor Holder',
      // Category management
      newCategory: 'New Category',
      categoryName: 'Category Name',
      categoryParent: 'Category Parent',
      status: 'Status',
      categoryNamePlaceholder: 'Please input category name',
      selectParentCategory: 'Please select parent category',
      parentCategoryRequired: 'Please select parent category',
      // Certificate selection
      selectCertificate: 'Select Certificate',
      name: 'Name',
      time: 'Time',
      creationTime: 'Creation Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      pleaseInputName: 'Please input name',
      // Exam selection
      selectOnlineExam: 'Select Online Exam',
      examName: 'Exam Name',
      subject: 'Subject',
      type: 'Type',
      examTime: 'Exam Time',
      assignedNumber: 'Assigned Number',
      creator: 'Creator',
      // Prerequisite course selection
      selectPrerequisitesCourse: 'Select Prerequisites Course'
    },
    class: {
      // Search form labels
      course: 'Course',
      type: 'Type',
      language: 'Language',
      publishStatus: 'Publish Status',
      status: 'Status',
      date: 'Date',
      title: 'Title',
      start: 'Start',
      end: 'End',
      pleaseInputTitle: 'Please input title',
      // Button labels
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      publish: 'Publish',
      join: 'Join',
      // Table column labels
      courseTitle: 'Course Title',
      classTitle: 'Class Title',
      classType: 'Class Type',
      trainer: 'Trainer',
      classroom: 'Classroom',
      duration: 'Duration',
      bookingNumber: 'Booking Number',
      action: 'Action',
      // Action buttons
      manage: 'Manage',
      copy: 'Copy',
      postpone: 'Postpone',
      cancel: 'Cancel',
      delete: 'Delete',
      // Dialog titles and content
      cancelClass: 'Cancel Class',
      confirmCancelClass: 'Are you confirming the Cancel Class',
      postponeClass: 'Postpone Class',
      confirmPostponeClass: 'Confirm Postpone Class',
      confirmPostponeAndMerge: 'Are you confirming the postpone Class and Merge with {name} Class',
      mergeSuccessful: 'Merge successful',
      offlineClass: 'Offline Class',
      virtualClass: 'Virtual Class',
      confirmPublishSchedule: 'Are you confirming the publishment of Monthly Schedule?',
      publishedSuccessfully: 'Published successfully',
      classPublishedCannotSelect: 'Class is published, cannot be selected',
      withdraw: 'Withdraw',
      withdrawClass: 'Withdraw Class',
      confirmWithdrawClass: 'Do you withdraw the Class?',
      confirmPublishClass: 'Do you publish the Class?',
      withdrawSuccessfully: 'Withdraw successfully',
      publishSuccessfully: 'Publish successfully',
      owner: 'Owner',
      startEndTime: 'Start&End Time',
      basicInformation: 'Basic Information',
      bookingManagement: 'Booking Management',
      checkInOut: 'Check-in/out',
      studentsManagement: 'Students Management',
      feedbackManagement: 'Feedback Management',
      materialsManagement: 'Materials Management',
      classRoster: 'Class Roster',
      classCode: 'Class Code',
      trainingDays: 'Training Days',
      translator: 'Translator',
      maximumAttendanceNum: 'Maximum Attendance Num',
      minimumAttendanceNum: 'Minimum Attendance Num',
      trainingDescription: 'Training Description',
      bookingInformation: 'Booking Information',
      allowedBookingTime: 'Allowed Booking Time',
      bookingRate: 'Booking Rate',
      bookingTime: 'Booking Time',
      prerequisite: 'Prerequisite',
      reviewProcess: 'Review Process',
      studentInformation: 'Student Information',
      badgeNo: 'Badge No.',
      position: 'Position',
      fullRefresher: 'Full/Refresher',
      full: 'Full',
      refresher: 'Refresher',
      approve: 'Approve',
      reject: 'Reject',
      approvalPassedSuccessfully: 'Approval passed successfully',
      approvalFailed: 'Approval failed',
      attachment: 'Attachment',
      bookingOverallProgress: 'Booking Overall Progress',
      bookingStatus: 'Booking Status',
      review: 'Review',
      checkInStudentNumber: 'Check-in Student Number',
      checkOutStudentNumber: 'Check-Out Student Number',
      attendanceStatus: 'Attendance Status',
      checkIn: 'Check-in',
      checkOut: 'Check-out',
      checkInTime: 'Check-in Time',
      checkOutTime: 'Check-out Time',
      scanQRCode: 'Scan QR Code',
      studentNumber: 'Student Number',
      completionRate: 'Completion Rate',
      notPassed: 'Not Passed',
      attendance: 'Attendance',
      companyName: 'Company Name',
      classStatus: 'Class Status',
      planned: 'Planned',
      actualAttendance: 'Actual attendance',
      noShow: 'No Show',
      attendanceRate: 'Attendance Rate',
      comments: 'Comments',
      evaluationTime: 'Evaluation Time',
      courseEvaluation: 'Course Evaluation',
      trainerEvaluation: 'Trainer Evaluation',
      facilityEvaluation: 'Facility Evaluation',
      messageNotification: 'Message Notification',
      notificationDescription: 'If your turn on the switch,students will receive relative notifications.',
      newClassTitle: 'New Class Title',
      className: 'Class Name:',
      publishClass: 'Publish Class',
      confirm: 'Confirm',
      // Form validation
      courseNameRequired: 'Course name cannot be empty',
      // Success messages
      cancelSuccessfully: 'Cancel successfully',
      // ClassForm specific labels
      startDate: 'Start Date',
      pickADate: 'Pick a Date',
      to: 'To',
      startTime: 'Start time',
      endTime: 'End time',
      days: 'Day(s)',
      pleaseSelectClassroom: 'Please select the Classroom',
      maximum: 'Maximum',
      minimum: 'Minimum',
      save: 'Save',
      // Form validation messages
      courseRequired: 'Course cannot be empty',
      courseTypeRequired: 'Course type cannot be empty',
      trainerRequired: 'Trainer cannot be empty',
      durationRequired: 'Duration cannot be empty',
      startDateRequired: 'Start Date cannot be empty',
      student: 'Student',
      // AssignClass specific labels (only unique ones, others use common)
      time: 'Time'
    },
    internal: {
      // Search form labels
      type: 'Type',
      place: 'Place',
      company: 'Company',
      time: 'Time',
      courseTitleEnglish: 'Course Title(English)',
      courseTitleArabic: 'Course Title(Arabic)',
      // Table column labels
      trainingCode: 'Training Code',
      courseTitleEN: 'Course Title EN',
      courseTitleAR: 'Course Title AR',
      startEndDate: 'Start & End Date',
      courseDuration: 'Course Duration',
      studentNumber: 'Student Number',
      studentList: 'Student List',
      notes: 'Notes',
      days: 'days',
      // Form labels
      form: {
        trainingCode: 'Training Code',
        courseTitleEN: 'Course Title EN',
        courseTitleAR: 'Course Title AR',
        trainingType: 'Training Type',
        place: 'Place',
        startEndTime: 'Start & End Time',
        duration: 'Duration',
        implementingCompany: 'Implementing Company',
        attachment: 'Attachment',
        comments: 'Comments',
        studentInformation: 'Student Information',
        multipleGroups: 'Multiple Groups',
        enableStudentsByMultipleGroups: 'Enable Students by Multiple Groups',
        addStudent: 'Add Student',
        groupNo: 'Group No.',
        name: 'Name',
        arabicName: 'Arabic Name',
        position: 'Position',
        notes: 'Notes',
        action: 'Action',
        remove: 'Remove',
        start: 'Start',
        end: 'End',
        // Validation messages
        courseTitleENRequired: 'Course Title EN cannot be empty',
        courseTitleARRequired: 'Course Title AR cannot be empty',
        trainingCodeRequired: 'Training Code cannot be empty',
        trainingTypeRequired: 'Training Type cannot be empty',
        placeRequired: 'Place cannot be empty',
        startEndTimeRequired: 'Start and end times cannot be empty',
        durationRequired: 'Duration cannot be empty',
        implementingCompanyRequired: 'Implementing Company cannot be empty'
      }
    },
    external: {
      // Search form labels
      country: 'Country',
      costBearer: 'Cost Bearer',
      travelDate: 'Travel Date',
      returnDate: 'Return Date',
      courseTitleEnglish: 'Course Title(English)',
      courseTitleArabic: 'Course Title(Arabic)',
      startDate: 'Start Date',
      endDate: 'End Date',
      // Table column labels
      trainingCode: 'Training Code',
      courseTitleEN: 'Course Title EN',
      courseTitleAR: 'Course Title AR',
      receivingCountry: 'The receiving Country',
      adminNo: 'Admin No.',
      studentNumber: 'Student Number',
      studentList: 'Student List',
      // Form labels
      travelReturn: 'Travel & Return',
      travel: 'Travel',
      return: 'Return',
      receivingCountryLabel: 'The Receiving Country',
      constBearer: 'Const Bearer',
      attachment: 'Attachment',
      comments: 'Comments',
      studentInformation: 'Student Information',
      multipleGroups: 'Multiple Groups',
      enableStudentsByMultipleGroups: 'Enable Students by Multiple Groups',
      addStudent: 'Add Student',
      groupNo: 'Group No.',
      name: 'Name',
      arabicName: 'Arabic Name',
      bocBadgeNo: 'BOC Badge No.',
      position: 'Position',
      department: 'Department',
      // Validation messages
      courseTitleENRequired: 'Course Title EN cannot be empty',
      courseTitleARRequired: 'Course Title AR cannot be empty',
      trainingCodeRequired: 'Training Code cannot be empty',
      receivingCountryRequired: 'Receiving Country cannot be empty',
      travelReturnRequired: 'Travel and Return Date cannot be empty',
      adminNoRequired: 'Admin No cannot be empty',
      costBearerRequired: 'Cost Bearer cannot be empty'
    },
  },
  bpm: {
    toDo: {
      taskTitlePlaceholder: 'Please enter task name',
      categoryPlaceholder: 'Please select process category',
      process: 'Process Belongs To',
      processPlaceholder: 'Please select process definition',
      startTime: 'Start Time',
      processName: 'Process',
      summary: 'Summary',
      initiator: 'Initiator',
      currentTask: 'Current Task',
      taskTime: 'Task Time',
      processInstanceId: 'Process Instance ID',
      taskId: 'Task ID',
      handle: 'Handle',
      processStatus: 'Process Status',
      processStatusApproving: 'Approving',
      processStatusPerson: 'Person',
      processStatusWait: 'Wait',
      processStatusPlaceholder: 'Please select process status',
      processCategory: 'Process Category',
      processStartFailed: 'Failed to restart the process. Reason: This process uses a business form and does not support restarting.',
      processCancelReasonPlaceholder: 'Please enter the reason for cancellation',
      processCancel: 'Cancel Process',
      processCancelReasonRequired: 'Cancellation reason cannot be empty',
      processCancelSuccess: 'Cancelled successfully'
    },
    processInstance: {
      operationButtonApprove: 'Approve',
      operationButtonReject: 'Reject',
      operationButtonTransfer: 'Transfer',
      operationButtonDelegate: 'Delegate',
      operationButtonAddSign: 'Countersign',
      operationButtonReturn: 'Return',
      operationButtonCopy: 'Copy',
      processInstanceDetailTitle: 'Approval Details',
      flowChartTitle: 'Flow Chart',
      circulationRecordTitle: 'Circulation Record',
      number: 'Number',
      submit: 'Submit',
      approve: 'Approval Opinion',
      approvePlaceholder: 'Please enter approval opinion',
      processNode: 'Approval Node',
      approver: 'Approver',
      status: 'Approval Status',
      approveSuggestion: 'Approval Suggestion',
      viewForm: 'View Form',
      costTime: 'Time Spent',
      formDetail: 'Form Details',
      approveTitle: 'Approve',
      approveOpinionTitle: 'Opinion',
      pleaseInput: 'Please enter',
      approveOpinionRequired: 'Approval opinion cannot be empty',
    }
  }
}
