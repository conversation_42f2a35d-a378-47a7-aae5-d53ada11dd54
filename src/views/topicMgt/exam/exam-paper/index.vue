<script setup lang="ts" name="ExamPaper">
import type { ComponentInternalInstance } from 'vue'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import {delPaper, listPaper, PaperRespVO, updatePaperStatus} from '@/api/topicMgt/paper'
import { listTopicAll } from '@/api/category/topic'
import { listDept } from '@/api/system/dept'
import { handlePhaseTree } from '@/utils/tree'
import { dateFormatter } from "@/utils/formatTime"

const router = useRouter()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const isVisible = ref<boolean>(false)
const tableData = ref<Array<PaperRespVO>>([])
const total = ref(0)
const queryRef = ref()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    departmentId: undefined,
    classifyId: undefined,
    status: undefined,
  },
})
const { queryParams } = toRefs(data)
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject')
  },
])
const deptList = ref()
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    departmentId: undefined,
    classifyId: undefined,
    status: undefined,
  }
  queryRef.value?.resetFields()
}
const handleAdd = () => {
  isVisible.value = true
}
// 编辑试卷
const handleEdit = (row: PaperRespVO) => {
  router.push({ name: 'PaperEdite', query: { id: row.id } })
}
// 删除试卷
const handleDelete = async (row: PaperRespVO) => {
  const noticeIds = row.id
  await message.confirm(`${t('global.deleteTip') + row.name}?`)
  await delPaper(noticeIds)
  await getList()
  message.success(t('global.deleteSuccess'))
}
const getList = async () => {
  loading.value = true
  try {
    const res = await listPaper(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
// 创建定制试卷
const handleCustomizedPaper = () => {
  isVisible.value = false
  router.push({ name: 'CustomPaper' })
}
// 创建随机试卷
const handleAutoPaper = () => {
  isVisible.value = false
  router.push({ name: 'AutoPaper' })
}
// 修改试卷状态
const handleRowStatus = async (row) => {
  await updatePaperStatus(row.id, row.status)
  message.success(t('global.submitSuccess'))
}
/** 查询部门列表 */
const getDeptList = async () => {
  const data = await listDept()
  deptList.value = handlePhaseTree(data, 'id')
}
// getDeptList()
onMounted(() => {
  getList()
  getSubjectData()
})
watch(
  () => isVisible,
  (newProps: any) => {
    isVisible.value = newProps
  },
)
// 监听当前路由 刷新列表数据
watch(
  () => router.currentRoute.value,
  (newValue: any, oldValue: any) => {
    getList()
  },
  { immediate: true },
)
</script>

<template>
  <div class="app-container">
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('examMgt.paper.name')">
          <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <!-- <el-form-item :label="$t('sys.user.department')" prop="departmentId">
          <el-tree-select
            v-model="queryParams.departmentId" :data="deptList"
            :props="{ value: 'id', label: 'name', children: 'children' }" value-key="deptId" check-strictly
            clearable
            style="width: 240px;"
          />
        </el-form-item> -->
        <el-form-item :label="t('common.status')">
          <el-select v-model="queryParams.status" :placeholder="t('common.chooseText')" clearable class="!w-160px">
            <el-option :label="t('global.enable')" value="1" />
            <el-option :label="t('global.disable')" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('category.topic.subjectName')">
          <!-- <el-select v-model="queryParams.classifyId" placeholder="Please choose" clearable style="width: 200px">
            <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
          <SubjectSelect v-model="queryParams.classifyId" :has-no-subject="true" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData">
        <el-table-column prop="name" :label="t('learningCenter.paper.paperName')" min-width="240" />
        <el-table-column prop="classifyName" :label="t('category.topic.subjectName')" min-width="180">
          <template #default="{ row }">
            {{ row.classifyId === 0 ? t('common.noSubject') : row.classifyName }}
          </template>
        </el-table-column>
        <el-table-column prop="isRandom" :label="t('learningCenter.course.type')" min-width="160">
          <template #default="{ row }">
            {{ row.isRandom === true ? t('learningCenter.exam.autoPaper') : t('learningCenter.exam.customizedPaper') }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="deptId" label="Department" width="150" align="center" /> -->
        <el-table-column prop="status" :label="t('common.status')" min-width="100">
          <template #default="{ row }">
            <el-switch v-model="row.status" size="large" :active-value="1" :inactive-value="0" @change="handleRowStatus(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="createBy" :label="t('category.journey.creator')" min-width="150" />
        <el-table-column prop="createTime" :label="t('category.journey.creationTime')" :formatter="dateFormatter" min-width="180" />
        <el-table-column fixed="right" :label="t('global.action')" min-width="150">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEdit(row)">
              <Icon icon="ep:edit" /> {{ t('action.edit') }}
            </el-button>
            <el-button link type="primary" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>


    <Dialog v-model="isVisible" :title="t('common.chooseText')">
      <el-row>
        <el-col :span="12">
          <div class="color_shadow w-[230px] h-[200px] m-auto cursor-pointer bg-[#007943] rounded-[10px]" @click="handleCustomizedPaper">
            <div class="text-white font-bold text-center leading-[50px]">
              {{ t('learningCenter.exam.customizedPaper') }}
            </div>
            <div class="bg-white h-[147px] rounded-b-[10px] p-[20px] pt-[10px] text-center font-medium">
              {{ t('examMgt.paper.pleaseChooseTheQuestion') }}
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="color_shadow w-[230px] h-[200px] m-auto cursor-pointer bg-[#007943] rounded-[10px]" @click="handleAutoPaper">
            <div class="text-white font-bold text-center leading-[50px]">
              {{ t('learningCenter.exam.autoPaper') }}
            </div>
            <div class="bg-white h-[147px] rounded-b-[10px] p-[20px] pt-[10px] text-center font-medium">
              {{ t('examMgt.paper.autoPaperPH') }}
            </div>
          </div>
        </el-col>
      </el-row>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.color_shadow {
  box-shadow: 0 0 8px 0 rgb(0 121 67 / 100%) !important;
}
</style>
