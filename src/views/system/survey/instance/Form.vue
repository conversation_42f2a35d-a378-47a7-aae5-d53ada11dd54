<template>
  <div class="instance-editor" v-loading="pageLoading" :element-loading-text="t('survey.loading')">
    <!-- Header -->
    <div class="editor-header">
      <div class="flex items-center">
        <el-button @click="handleBack" circle class="mr-4">
          <Icon icon="ep:arrow-left" />
        </el-button>
        <div>
          <h1 class="text-xl font-bold">
            {{ isEdit ? t('survey.editInstance') : t('survey.addInstance') }}
          </h1>
        </div>
      </div>
      <div class="flex gap-3">
        <el-button @click="handleSave" type="primary" :loading="saveLoading">
          <Icon icon="ep:check" class="mr-2" />
          {{ t('survey.save') }}
        </el-button>
<!--        <el-button-->
<!--          v-if="isEdit && formData.status === 0"-->
<!--          type="success"-->
<!--          @click="handlePublish"-->
<!--          :loading="publishLoading"-->
<!--        >-->
<!--          <Icon icon="ep:promotion" class="mr-1" />-->
<!--          {{ t('survey.publishInstance') }}-->
<!--        </el-button>-->
        <el-button @click="handleBack">
          {{ t('survey.cancel') }}
        </el-button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="editor-content">
      <!-- Left Panel: Content Tabs -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>{{ t('survey.instanceContent') }}</h3>
        </div>

        <div class="content-tabs">
          <div
            v-for="tab in contentTabs"
            :key="tab.name"
            class="tab-item"
            :class="{ active: activeTab === tab.name }"
            @click="activeTab = tab.name"
          >
            <div class="tab-content">
              <div class="tab-icon">
                <Icon :icon="tab.icon" />
              </div>
              <div class="tab-info">
                <div class="tab-name">{{ tab.label }}</div>
<!--                <div class="tab-desc">{{ tab.description }}</div>-->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Content Editor -->
      <div class="right-panel">
        <!-- 基础信息 -->
        <div v-if="activeTab === 'basic'" class="content-section">
          <div class="section-header">
            <h3 class="section-title">{{ t('survey.basicInfo') }}</h3>
          </div>
          <div class="section-content">
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="160px"
              class="basic-form"
            >
              <el-form-item :label="t('survey.instanceName')" prop="name">
                <el-input
                  v-model="formData.name"
                  :placeholder="t('survey.pleaseEnter') + t('survey.instanceName')"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>

<!--              <el-form-item :label="t('survey.template')" prop="templateId">-->
<!--                <div class="template-selector">-->
<!--                  <el-input-->
<!--                    :value="selectedTemplate?.name || ''"-->
<!--                    :placeholder="t('survey.pleaseSelect') + t('survey.template')"-->
<!--                    readonly-->
<!--                    class="w-full"-->
<!--                  >-->
<!--                    <template #suffix>-->
<!--                      <el-button-->
<!--                        type="primary"-->
<!--                        link-->
<!--                        @click="handleSelectTemplate"-->
<!--                        class="select-btn"-->
<!--                      >-->
<!--                        {{ t('survey.selectTemplate') }}-->
<!--                      </el-button>-->
<!--                    </template>-->
<!--                  </el-input>-->
<!--                </div>-->
<!--              </el-form-item>-->

              <el-form-item :label="t('survey.timeRange')" prop="timeRange">
                <div class="time-range-selector">
                  <el-date-picker
                    v-model="formData.startTime"
                    type="datetime"
                    :placeholder="t('survey.startTime')"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 45%"
                  />
                  <span class="time-separator">-</span>
                  <el-date-picker
                    v-model="formData.endTime"
                    type="datetime"
                    :placeholder="t('survey.endTime')"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 45%"
                  />
                </div>
              </el-form-item>

              <el-form-item :label="t('survey.department')" prop="deptId">
                <el-cascader
                  v-model="formData.deptId"
                  :options="deptOptions"
                  :props="deptCascaderProps"
                  :placeholder="t('survey.pleaseSelect') + t('survey.department')"
                  filterable
                  clearable
                  class="w-full"
                  :show-all-levels="false"
                  @change="handleDeptChange"
                />
              </el-form-item>

              <el-form-item :label="t('survey.instanceStatus')" v-if="isEdit">
                <el-tag :type="getInstanceStatusType(formData.status)" size="large">
                  {{ getInstanceStatusText(formData.status) }}
                </el-tag>
              </el-form-item>

              <el-form-item :label="t('survey.anonymous')" prop="anonymousEnabled">
                <el-switch
                  v-model="formData.anonymousEnabled"
                  :active-text="t('common.yes')"
                  :inactive-text="t('common.no')"
                />
              </el-form-item>

              <el-form-item :label="t('survey.scopeType')" prop="scopeType">
                <el-radio-group v-model="formData.scopeType">
                  <el-radio :value="1">{{ t('survey.public') }}</el-radio>
                  <el-radio :value="2">{{ t('survey.internal') }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item :label="t('survey.submitFrequency')" prop="submitFrequency">
                <el-select
                  v-model="formData.submissionFrequency"
                  :placeholder="t('survey.pleaseSelect') + t('survey.submitFrequency')"
                  class="w-full"
                >
                  <el-option
                    v-for="option in submitFrequencyOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="formData.submissionFrequency === SubmissionFrequencyEnum.MULTIPLE"
                :label="t('survey.maxSubmissions')"
                prop="maxSubmissions"
              >
                <el-input-number
                  v-model="formData.maxSubmissions"
                  :min="1"
                  :max="100"
                  :placeholder="t('survey.pleaseEnter') + t('survey.maxSubmissions')"
                  class="w-full"
                />
              </el-form-item>

              <el-form-item :label="t('survey.maxResponses')" prop="maxResponses">
                <el-input-number
                  v-model="formData.maxResponses"
                  :min="1"
                  :max="10000"
                  :placeholder="t('survey.pleaseEnter') + t('survey.maxResponses')"
                  class="w-full"
                  clearable
                />
                <div class="form-item-tip">
                  {{ t('survey.maxResponsesTip') }}
                </div>
              </el-form-item>

<!--              <el-form-item :label="t('survey.allowViewStatistics')" prop="allowViewStatistics">-->
<!--                <el-switch-->
<!--                  v-model="formData.allowViewStatistics"-->
<!--                  :active-text="t('common.yes')"-->
<!--                  :inactive-text="t('common.no')"-->
<!--                />-->
<!--              </el-form-item>-->

              <el-form-item :label="t('survey.description')" prop="description">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="4"
                  :placeholder="t('survey.pleaseEnter') + t('survey.description')"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 模板信息 -->
        <div v-if="activeTab === 'template'" class="content-section">
          <div class="section-header">
            <h3 class="section-title">{{ t('survey.templateInfo') }}</h3>
            <el-button type="primary" @click="handleSelectTemplate">
              {{ t('survey.changeTemplate') }}
            </el-button>
          </div>
          <div class="section-content">
            <div v-if="selectedTemplate" class="template-preview-section">
              <!-- 模板预览组件 -->
              <TemplatePreview
                ref="templatePreviewRef"
                :template-id="formData.templateId"
                :template="selectedTemplate"
              />
            </div>
            <div v-else class="empty-state">
              <el-empty :description="t('survey.pleaseSelectTemplate')" />
            </div>
          </div>
        </div>

        <!-- 作用域配置 -->
        <div v-if="activeTab === 'scope'" class="content-section">
          <div class="section-header">
            <h3 class="section-title">{{ t('survey.scopeConfiguration') }}</h3>
            <el-button type="primary" @click="handleAddScope" v-if="formData.scopeType === ScopeTypeEnum.INTERNAL">
              <Icon icon="ep:plus" class="mr-1" />
              {{ t('survey.addScope') }}
            </el-button>
          </div>
          <div class="section-content">
            <div v-if="formData.scopeType === ScopeTypeEnum.INTERNAL" class="scope-section">
              <div v-if="formData.scopes && formData.scopes.length > 0" class="scope-list">
                <div class="table-container">
                  <el-table :data="formData.scopes" style="width: 100%">
                    <el-table-column :label="t('survey.targetType')" width="120">
                      <template #default="scope">
                        <el-tag :type="getScopeTypeColor(scope.row.targetType)" size="small">
                          {{ getScopeTypeText(scope.row.targetType) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column :label="t('survey.targetName')" prop="targetName" />
                    <el-table-column :label="t('survey.actions')" align="center" width="100">
                      <template #default="scope">
                        <el-button
                          link
                          type="danger"
                          size="small"
                          @click="handleRemoveScope(scope.$index)"
                        >
                          {{ t('survey.delete') }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div v-else class="empty-state">
                <el-empty :description="t('survey.noScopeConfigured')" />
              </div>
            </div>
            <div v-else class="public-notice">
              <el-alert
                :title="t('survey.publicSurveyNoScope')"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div v-if="activeTab === 'statistics' && isEdit" class="content-section">
          <SurveyStatistics
            :instance-id="instanceId!"
            ref="surveyStatisticsRef"
          />
        </div>
      </div>
    </div>

    <!-- 模板选择弹窗 -->
    <TemplateSelectDialog
      v-model:visible="templateDialogVisible"
      :selected-template="selectedTemplate"
      @confirm="handleTemplateConfirm"
    />

    <!-- 作用域选择弹窗 -->
    <el-dialog
      v-model="scopeDialogVisible"
      :title="t('survey.scopeConfig')"
      width="1200px"
      :close-on-click-modal="false"
      @closed="handleScopeDialogClosed"
    >
      <SurveyScopeSelect
        ref="surveyScopeSelectRef"
        v-model="scopeList"
        v-model:loading="scopeLoading"
        @confirm="handleScopeConfirm"
        @delete="handleScopeDelete"
      />
      <template #footer>
        <el-button @click="scopeDialogVisible = false">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleScopeDialogConfirm">
          {{ t('common.save') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'

import { useRoute, useRouter } from 'vue-router'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { Icon } from '@/components/Icon'
import { InstanceApi } from '@/api/system/survey/instance'
import TemplateSelectDialog from '@/views/system/survey/template/TemplateSelectDialog.vue'
import TemplatePreview from '@/views/system/survey/template/TemplatePreview.vue'
import SurveyScopeSelect from '@/views/system/survey/instance/SurveyScopeSelect.vue'
import SurveyStatistics from '@/components/Survey/SurveyStatistics.vue'
import { TemplateApi } from '@/api/system/survey/template'
import { QuestionApi } from '@/api/system/survey/question'
import { StatisticsApi } from '@/api/system/survey/statistics'
import { DepartmentTreeRespVO, deptTreeSelect } from '@/api/system/user'
import {
  SurveyInstance,
  SurveyTemplate,
  SurveyQuestion,
  SubmissionFrequencyEnum,
  InstanceStatusEnum,
  ScopeTypeEnum,
  ScopeTargetTypeEnum,
  CommonStatusEnum
} from '@/api/system/survey/types'
import { useTagsViewStore } from '@/store/modules/tagsView'
const {back, push, currentRoute} = useRouter()

const { delVisitedView } = useTagsViewStore()

const { t } = useI18n()
const message = useMessage()
const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const saveLoading = ref(false)
const publishLoading = ref(false)
const statisticsLoading = ref(false)
const pageLoading = ref(false)
const activeTab = ref('basic')
const templateOptions = ref<SurveyTemplate[]>([])
const selectedTemplate = ref<SurveyTemplate | null>(null)
const templateQuestions = ref<SurveyQuestion[]>([])
const deptOptions = ref<DepartmentTreeRespVO[]>([])
const statistics = ref<any>(null)

// 模板选择弹窗相关
const templateDialogVisible = ref(false)

// 模板预览组件引用
const templatePreviewRef = ref()

// 作用域选择弹窗相关
const scopeDialogVisible = ref(false)
const surveyScopeSelectRef = ref()
const scopeList = ref<any[]>([])
const scopeLoading = ref(false)

// 统计组件引用
const surveyStatisticsRef = ref()


// 左侧标签页配置
const contentTabs = computed(() => {
  const tabs = [
    {
      name: 'basic',
      label: t('survey.basicInfo'),
      description: t('survey.basicInfoDesc'),
      icon: 'ep:document'
    },
    {
      name: 'template',
      label: t('survey.templateInfo'),
      description: t('survey.templateInfoDesc'),
      icon: 'ep:files'
    },
    {
      name: 'scope',
      label: t('survey.scopeConfiguration'),
      description: t('survey.scopeConfigDesc'),
      icon: 'ep:user'
    }
  ]

  // 只有在编辑模式下才显示统计标签页
  if (isEdit.value) {
    tabs.push({
      name: 'statistics',
      label: t('survey.statistics'),
      description: t('survey.statisticsDesc'),
      icon: 'ep:data-analysis'
    })
  }

  return tabs
})

// 计算属性
const isEdit = computed(() => route.query.type === 'update')
const instanceId = computed(() => (route.query.id ? Number(route.query.id) : null))

// 表单数据
const formData = reactive<Partial<SurveyInstance>>({
  name: '',
  templateId: undefined,
  startTime: '',
  endTime: '',
  deptId: undefined,
  status: InstanceStatusEnum.UNPUBLISHED,
  anonymousEnabled: false,
  scopeType: ScopeTypeEnum.PUBLIC,
  submissionFrequency: SubmissionFrequencyEnum.ONCE,
  maxSubmissions: 2,
  maxResponses: undefined,
  allowViewStatistics: false,
  description: '',
  scopes: []
})

// 表单验证规则
const formRules = reactive({
  name: [
    { required: true, message: t('survey.pleaseEnter') + t('survey.instanceName'), trigger: 'blur' }
  ],
  // templateId: [
  //   { required: true, message: t('survey.pleaseSelect') + t('survey.template'), trigger: 'change' }
  // ],
  startTime: [
    { required: true, message: t('survey.pleaseSelect') + t('survey.startTime'), trigger: 'change' }
  ],
  endTime: [
    { required: true, message: t('survey.pleaseSelect') + t('survey.endTime'), trigger: 'change' }
  ]
})

// 选项数据
const instanceStatusOptions = computed(() => [
  { label: t('survey.unpublished'), value: InstanceStatusEnum.UNPUBLISHED },
  { label: t('survey.ongoing'), value: InstanceStatusEnum.ONGOING },
  { label: t('survey.ended'), value: InstanceStatusEnum.ENDED },
  { label: t('survey.draft'), value: InstanceStatusEnum.DRAFT }
])

const submitFrequencyOptions = computed(() => [
  { label: t('survey.onceOnly'), value: SubmissionFrequencyEnum.ONCE },
  { label: t('survey.multiple'), value: SubmissionFrequencyEnum.MULTIPLE },
  { label: t('survey.unlimited'), value: SubmissionFrequencyEnum.UNLIMITED }
])

// 部门级联选择器配置
const deptCascaderProps = {
  value: 'id',
  children: 'children',
  emitPath: false, // 只返回选中节点的值，不返回完整路径
  checkStrictly: true // 允许选择任意级别的节点
}

// 获取实例状态显示文本
const getInstanceStatusText = (status: number) => {
  const statusMap = {
    [InstanceStatusEnum.UNPUBLISHED]: t('survey.unpublished'),
    [InstanceStatusEnum.ONGOING]: t('survey.ongoing'),
    [InstanceStatusEnum.ENDED]: t('survey.ended'),
    [InstanceStatusEnum.DRAFT]: t('survey.draft')
  }
  return statusMap[status] || t('survey.unknown')
}

// 获取实例状态标签类型
const getInstanceStatusType = (status: number) => {
  const typeMap = {
    [InstanceStatusEnum.UNPUBLISHED]: 'info',
    [InstanceStatusEnum.ONGOING]: 'success',
    [InstanceStatusEnum.ENDED]: 'warning',
    [InstanceStatusEnum.DRAFT]: 'default'
  }
  return typeMap[status] || 'info'
}

/** 时间格式转换工具函数 */
const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const parseTimeString = (timeString: string): number => {
  return new Date(timeString).getTime()
}

/** 时间格式转换工具函数 */
const formatTimestampToString = (timestamp: number | string | null): string => {
  if (!timestamp) return ''

  const date = new Date(typeof timestamp === 'string' ? parseInt(timestamp) : timestamp)
  if (isNaN(date.getTime())) return ''

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const formatStringToTimestamp = (dateString: string | null): number | null => {
  if (!dateString) return null

  const date = new Date(dateString)
  if (isNaN(date.getTime())) return null

  return date.getTime()
}

/** 初始化 */
onMounted(async () => {
  await loadDeptOptions()
  if (isEdit.value && instanceId.value) {
    await loadInstanceData()
  }
})

/** 转换部门树形数据格式 */
const convertDeptTreeData = (depts: any[]): any[] => {
  return depts.map((dept) => ({
    value: dept.id,
    label: dept.label ,
    children: dept.children && dept.children.length > 0 ? convertDeptTreeData(dept.children) : undefined
  }))
}

/** 加载部门选项 */
const loadDeptOptions = async () => {
  try {
    const params = { status: 0 } // 只获取启用的部门，使用数字类型
    const data = await deptTreeSelect(params)
    console.log('Department tree data:', data) // 调试日志
    // 转换为级联选择器需要的格式
    // deptOptions.value = convertDeptTreeData(data || [])
    deptOptions.value = data
    console.log('Converted dept options:', deptOptions.value) // 调试日志

  } catch (error) {
    console.error('Failed to load dept options:', error)
    deptOptions.value = []
  }
}


/** 根据部门ID查找路径 */
const findDeptPath = (deptId: number, depts: any[], path: number[] = []): number[] | null => {
  for (const dept of depts) {
    const currentPath = [...path, dept.value]

    if (dept.value === deptId) {
      return currentPath
    }

    if (dept.children && dept.children.length > 0) {
      const result = findDeptPath(deptId, dept.children, currentPath)
      if (result) {
        return result
      }
    }
  }
  return null
}

/** 处理部门选择变化 */
const handleDeptChange = (value: any) => {
  console.log('Department changed:', value) // 调试日志

  formData.deptId = value

  console.log('FormData deptId updated to:', formData.deptId)
}



/** 加载实例数据 */
const loadInstanceData = async () => {
  if (!instanceId.value) return

  try {
    pageLoading.value = true
    const data = await InstanceApi.get(instanceId.value)

    // 转换时间戳为字符串格式，供时间选择器使用
    Object.assign(formData, {
      ...data,
      startTime: formatTimestampToString(data.startTime),
      endTime: formatTimestampToString(data.endTime),
      scopes: data.scopes || []
    })

    // 加载模板信息
    if (data.templateId) {
      await loadTemplateInfo(data.templateId)
    }
  } catch (error) {
    console.error('Failed to load instance data:', error)
    message.error(t('common.loadFailed'))
  } finally {
    pageLoading.value = false
  }
}

/** 加载模板信息 */
const loadTemplateInfo = async (templateId: number) => {
  try {
    const template = await TemplateApi.get(templateId)
    selectedTemplate.value = template

    // 先加载问题数据
    await loadTemplateQuestions(templateId)

    // 模板预览组件会通过props自动更新，无需手动调用
  } catch (error) {
    console.error('Failed to load template info:', error)
  }
}

/** 加载模板问题 */
const loadTemplateQuestions = async (templateId: number) => {
  try {
    const questions = await QuestionApi.getByTemplate(templateId)
    templateQuestions.value = questions || []
  } catch (error) {
    console.error('Failed to load template questions:', error)
    templateQuestions.value = []
  }
}

/** 获取统计信息 */
const getStatistics = async () => {
  if (!isEdit.value || !instanceId.value) return

  try {
    statisticsLoading.value = true
    const data = await StatisticsApi.get({
      instanceId: instanceId.value,
      onlyLatest: true
    })
    statistics.value = data
  } catch (error) {
    console.error('Failed to load statistics:', error)
    message.error(t('survey.loadStatisticsFailed'))
  } finally {
    statisticsLoading.value = false
  }
}

/** 模板选择相关方法 */
const handleSelectTemplate = () => {
  templateDialogVisible.value = true
}

/** 模板选择确认 */
const handleTemplateConfirm = (template: SurveyTemplate) => {
  formData.templateId = template.id
  selectedTemplate.value = template
  loadTemplateInfo(template.id)

  // 切换到模板信息标签页
  activeTab.value = 'template'
}



/** 作用域配置相关方法 */
const handleAddScope = () => {
  scopeDialogVisible.value = true
  // 加载当前已选择的作用域数据
  loadCurrentScopeData()
}

/** 加载当前已选择的作用域数据 */
const loadCurrentScopeData = () => {
  if (formData.scopes && formData.scopes.length > 0) {
    scopeList.value = formData.scopes.map(scope => ({
      id: scope.targetId,
      name: scope.targetName,
      type: scope.targetType
    }))
  } else {
    scopeList.value = []
  }
}

/** SurveyScopeSelect组件的确认事件 */
const handleScopeConfirm = (newScopes: any[]) => {
  // 添加新的作用域到formData.scopes
  if (!formData.scopes) {
    formData.scopes = []
  }

  const scopesToAdd = newScopes.map(scope => ({
    targetType: scope.type,
    targetId: scope.id,
    targetName: scope.name
  }))

  formData.scopes = [...formData.scopes, ...scopesToAdd]
  console.log('Added scopes:', scopesToAdd)
}

/** SurveyScopeSelect组件的删除事件 */
const handleScopeDelete = (deletedScopes: any[]) => {
  if (!formData.scopes) return

  // 从formData.scopes中移除删除的作用域
  const deletedIds = Array.isArray(deletedScopes)
    ? deletedScopes.map(scope => ({ id: scope.id, type: scope.type }))
    : [{ id: deletedScopes.id, type: deletedScopes.type }]

  formData.scopes = formData.scopes.filter(scope =>
    !deletedIds.some(deleted =>
      deleted.id === scope.targetId && deleted.type === scope.targetType
    )
  )

  console.log('Deleted scopes:', deletedScopes)
}

/** 作用域弹窗确认 */
const handleScopeDialogConfirm = () => {
  // 将scopeList转换为formData.scopes格式
  formData.scopes = scopeList.value.map(scope => ({
    targetType: scope.type,
    targetId: scope.id,
    targetName: scope.name
  }))

  scopeDialogVisible.value = false
  message.success(t('survey.scopeConfigSuccess'))
}

/** 作用域弹窗关闭 */
const handleScopeDialogClosed = () => {
  // 重置状态
  scopeList.value = []
}





/** 删除作用域 */
const handleRemoveScope = (index: number) => {
  if (formData.scopes && formData.scopes.length > index) {
    formData.scopes.splice(index, 1)
    message.success(t('survey.scopeRemovedSuccess'))
  }
}

/** 获取问题类型文本 */
const getQuestionTypeText = (type: number) => {
  const types = {
    1: t('survey.singleChoice'),
    2: t('survey.multipleChoice'),
    3: t('survey.trueFalse'),
    4: t('survey.rating'),
    5: t('survey.fileUpload'),
    6: t('survey.text')
  }
  return types[type] || t('survey.unknown')
}

/** 获取问题类型颜色 */
const getQuestionTypeColor = (type: number) => {
  const colors = {
    1: 'primary',
    2: 'success',
    3: 'info',
    4: 'warning',
    5: 'danger',
    6: 'primary'
  }
  return colors[type] || 'primary'
}

/** 获取作用域类型文本 */
const getScopeTypeText = (type: number) => {
  const types = {
    [ScopeTargetTypeEnum.USER]: t('survey.user'),
    [ScopeTargetTypeEnum.DEPT]: t('survey.department'),
    [ScopeTargetTypeEnum.ROLE]: t('survey.role')
  }
  return types[type] || t('survey.unknown')
}

/** 获取作用域类型颜色 */
const getScopeTypeColor = (type: number) => {
  const colors = {
    [ScopeTargetTypeEnum.USER]: 'primary',
    [ScopeTargetTypeEnum.DEPT]: 'success',
    [ScopeTargetTypeEnum.ROLE]: 'warning'
  }
  return colors[type] || 'primary'
}

/** 返回列表 */
const handleBack = () => {
  delVisitedView(unref(currentRoute))
  push({
    path: `/survey/instance-list`
  })
}

/** 保存 */
const handleSave = async () => {
  try {
    // 只在基本信息标签时进行表单验证
    if (activeTab.value === 'basic' && formRef.value) {
      await formRef.value.validate()
    }

    saveLoading.value = true

    // 准备保存数据，将时间字符串转换为时间戳
    const saveData: any = {
      templateId: formData.templateId,
      deptId: formData.deptId,
      name: formData.name,
      description: formData.description,
      startTime: formatStringToTimestamp(formData.startTime),
      endTime: formatStringToTimestamp(formData.endTime),
      status: formData.status,
      scopeType: formData.scopeType,
      anonymousEnabled: formData.anonymousEnabled,
      submissionFrequency: formData.submissionFrequency,
      maxSubmissions: formData.maxSubmissions,
      maxResponses: formData.maxResponses,
      submissionInterval: formData.submissionInterval,
      allowViewStatistics: formData.allowViewStatistics,
      scopes: formData.scopes?.map(scope => ({
        targetType: scope.targetType,
        targetId: scope.targetId
      })) || []
    }

    if (isEdit.value && instanceId.value) {
      saveData.id = instanceId.value
      await InstanceApi.update(saveData)
      message.success(t('common.updateSuccess'))
    } else {
      await InstanceApi.create(saveData)
      message.success(t('common.createSuccess'))
      // 清除当前页面的访问记录并跳转到列表页面
      delVisitedView(unref(currentRoute))
      await router.push('/survey/instance-list')
    }
  } catch (error) {
    console.error('Failed to save instance:', error)
    if (error.message && error.message.includes('validation')) {
      message.error(t('survey.pleaseCheckFormData'))
    } else {
      message.error(t('common.saveFailed'))
    }
  } finally {
    saveLoading.value = false
  }
}

/** 发布 */
const handlePublish = async () => {
  if (!instanceId.value) return

  try {
    publishLoading.value = true
    await InstanceApi.publish(instanceId.value)
    formData.status = 1
    message.success(t('survey.publishSuccess'))
  } catch (error) {
    console.error('Failed to publish instance:', error)
    message.error(t('survey.publishFailed'))
  } finally {
    publishLoading.value = false
  }
}
</script>

<style scoped>
.instance-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  overflow-y: auto;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.editor-content {
  flex: 1;
  display: flex;
}

/* Left Panel */
.left-panel {
  width: 260px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.content-tabs {
  flex: 1;
  overflow-y: auto;
}

.tab-item {
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #e4e7ed;
}

.tab-item:last-child {
  border-bottom: none;
}

.tab-item:hover .tab-content {
  background: #f5f7fa;
}

.tab-item.active .tab-content {
  background: #ecf5ff;
  border-left: 3px solid #409eff;
}

.tab-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  transition: all 0.2s ease;
}

.tab-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  color: #606266;
  border-radius: 6px;
  margin-right: 12px;
  font-size: 16px;
  transition: all 0.2s ease;
}

.tab-item.active .tab-icon {
  background: #409eff;
  color: white;
}

.tab-info {
  flex: 1;
}

.tab-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 16px;
}

.tab-item.active .tab-name {
  color: #409eff;
  font-weight: 600;
}

.tab-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* Right Panel */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 0;
  background: white;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-content {
  flex: 1;
  padding: 20px 24px;
  background: white;
  overflow-y: auto;
}

.basic-form {
  max-width: 600px;
}

/* 时间范围选择器样式 */
.time-range-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.time-separator {
  color: #909399;
  font-weight: 500;
}

/* 模板选择器样式 */
.template-selector {
  position: relative;
}

.select-btn {
  padding: 0 8px;
  font-size: 12px;
}

/* 信息展示样式 */
.template-basic-info {
  margin-bottom: 24px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  gap: 40px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item.full-width {
  flex: 1;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
}

/* 问题列表样式 */
.questions-section {
  margin-top: 24px;
}

.questions-container {
  margin-top: 20px;
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.question-card {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px;
  transition: background-color 0.2s;
}

.question-card:hover {
  background: #f8f9fa;
}

.question-card:last-child {
  border-bottom: none;
}

.question-header {
  margin-bottom: 12px;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-number {
  background: #409eff;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
}

.question-body {
  padding-left: 28px;
}

.question-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 6px 0;
  line-height: 1.4;
}

.question-description {
  color: #909399;
  font-size: 12px;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.question-options {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 3px 0;
  font-size: 12px;
}

.option-label {
  font-weight: 500;
  color: #409eff;
  min-width: 16px;
}

.option-text {
  flex: 1;
  color: #606266;
}

.option-score {
  color: #67c23a;
  font-size: 11px;
  font-weight: 500;
}

/* 统计样式 */
.statistics-section {
  padding: 0;
}

.stats-overview {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 16px;
}

.stat-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s;
}

.stat-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.question-stats-section {
  margin-top: 24px;
}

.table-container {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.score-value {
  font-weight: 500;
  color: #409eff;
}

/* 弹窗样式 */
.template-dialog-content,
.scope-dialog-content {
  padding: 0;
}

.search-section {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
}

.template-list-section,
.scope-list-section {
  margin-top: 16px;
}

.pagination-section {
  margin-top: 16px;
  text-align: right;
}

.target-type-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.dialog-section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 12px 0;
}

/* 作用域相关样式 */
.scope-section {
  padding: 0;
}

.scope-list {
  margin-top: 16px;
}

.public-notice {
  padding: 20px 0;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.load-stats-section {
  padding: 60px 20px;
  text-align: center;
}

/* 搜索表单项样式 */
.search-form-item {
  margin-bottom: 0;
}

.search-form-item :deep(.el-form-item__label) {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.search-form-item :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  margin-left: 10px;
  line-height: 1.4;
}

/* Prevent form label text wrapping */
.basic-form :deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
