<template>
  <ContentWrap class="w-full h-full">
    <div>
      <el-radio-group v-model="scanType">
        <el-radio label="Check-in" value="1" />
        <el-radio label="Check-out" value="2" />
      </el-radio-group>
    </div>
    <el-button type="primary" @click="jumpBack">Back</el-button>
    <el-button type="primary" @click="changeScan">Scan QR Code</el-button>

    <div id="app" v-if="isScanning">
      <!--    使用<qrcode-stream>标签来创建扫码组件。-->
      <div id="qr-reader" style="width: 100%;"></div>
<!--      <qrcode-stream @decode="onDecode" @init="onInit" :showCanvasBounds="true" />-->
    </div>
    <!-- 人员选择弹框 -->
    <EmployeeSelect
      ref="selectEmployee"
      @confirm="employeeConfirm"
    />
  </ContentWrap>

</template>
<script lang="ts" setup>
defineOptions({ name: 'ScanQrCode' })

import { useUserStore } from "@/store/modules/user"
import { QrcodeStream } from 'vue-qrcode-reader'
import { Html5Qrcode } from "html5-qrcode"
import EmployeeSelect from '@/components/EmployeeExclusiveSelect/index.vue'
import { ClassInfoApi, ClassInfoRespVO } from '@/api/academy/class'
import { employeeData } from "@/api/system/user"
import { useTagsViewStore } from "@/store/modules/tagsView"
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const router = useRouter()
const scanType = ref()
const userStore = useUserStore()
const { push, currentRoute } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作

const loading = ref(true)
const isScanning = ref(false)
const selectEmployee = ref()
const formData = ref({
  userId: undefined,
  classId: route.query.id,
})
const queryParams = ref({
  pageNo: 1,
  pageSize: 9999,
  nickname: '',
})

// 回退
const jumpBack = () => {
  delView(unref(currentRoute))
  router.go(-1)

}
// 开启签到时间
const changeScan = async () => {
  if (!scanType.value) {
    message.warning('Please select a scan type')
    return
  }
  // 开始签到
  await ClassInfoApi.updateClassCheckInTime(route.query.id)
  isScanning.value = true
  // message.success('Update check-in succeeded')
}

// 签到签出
const checkScan = async () => {
  if (scanType.value === '1') {
    await ClassInfoApi.classCheckIn(formData.value)
    message.success('Check in successful')
  } else {
    await ClassInfoApi.classCheckOut(formData.value)
    message.success('Check out successful')
  }
  // 完成签到或者签出后再次开启扫描
  isScanning.value = true
}

// 监听@decode事件来获取扫码结果。
// const onDecode = async (content) => {
//   // 扫描之前先将之前的用户id清除以及搜索的用户名
//   formData.value.userId = undefined
//   queryParams.value.nickname = ''
//   if (content) {
//     // 扫描结果: HSE Passport No : MAJ25276 Name : SHAHZADA LIAQAT Company : Anton Oil 完成扫描后获取到的内容,需要单独获取到用户名称进行查询
//     const match = content.match(/Name\s*:\s*(.*?)\s*Company/)
//     if (match) {
//       queryParams.value.nickname = match[1].trim() // 使用 trim() 去除可能的多余空格
//     }
//     const data = await employeeData(queryParams.value)
//     if (data.list?.length > 1) {
//       // 如果匹配出来的用户多则弹框让管理员手动去选择给谁签到或者签出
//       selectEmployee.value.open(undefined,queryParams.value.nickName)
//     } else if (data.list?.length === 1) {
//       // 如果匹配的用户只有1个则直接签到或者签出
//       formData.value.userId = data.list[0].id
//       await checkScan()
//     } else {}
//   }
//   // 停止扫描
//   isScanning.value = false
// }


// 在methods中定义onInit方法来处理初始化错误。
// const onInit = (promise) => {
//   promise.then(() => {
//   }).catch(error => {
//     if (error.name === 'NotAllowedError') {
//       message.info('Please allow camera access to scan QR codes.')
//     } else if (error.name === 'NotFoundError') {
//       message.info('No camera found on this device.')
//     } else if (error.name === 'NotSupportedError') {
//       message.info('The browser does not support camera access.')
//     } else if (error.name === 'NotReadableError') {
//       message.info('Could not access the camera. Is it already in use?')
//     } else {
//       message.info('Unknown error occurred: ' + error.message)
//     }
//   })
// }

let qrReader = null
const startScan = async () => {
  qrReader = new Html5Qrcode("qr-reader")
  const config = { fps: 10, qrbox: 250 }

  // 扫描之前先将之前的用户id清除以及搜索的用户名
  formData.value.userId = undefined
  queryParams.value.nickname = ''

  try {
    await qrReader.start(
      { facingMode: "environment" },
      config,
      async (decodedResult) => {
        if (decodedResult) {
          // 扫描结果: HSE Passport No : MAJ25276 Name : SHAHZADA LIAQAT Company : Anton Oil 完成扫描后获取到的内容,需要单独获取到用户名称进行查询
          const match = decodedResult.match(/Name\s*:\s*(.*?)\s*Company/)
          if (match) {
            queryParams.value.nickname = match[1].trim(); // 使用 trim() 去除可能的多余空格
          }

          try {
            const data = await employeeData(queryParams.value)
            if (data.list?.length > 1) {
              // 搜索出多个用户先关闭扫描
              isScanning.value = false
              // 如果匹配出来的用户多则弹框让管理员手动去选择给谁签到或者签出
              selectEmployee.value.open(undefined, queryParams.value.nickname)
            } else if (data.list?.length === 1) {
              // 如果匹配的用户只有1个则直接签到或者签出
              formData.value.userId = data.list[0].id
              await checkScan()
            } else {
              // 可以添加处理没有找到用户的情况
              console.log("未找到匹配的员工")
            }
          } catch (apiError) {
            console.error("查询员工数据时出错:", apiError)
          }
        }
      }
    )


  } catch (err) {
    console.error('Error starting scanner:', err);
    // 可以在这里设置一个错误状态，以便在模板中显示
  }
};


onMounted(() => {
  startScan()
})

// 确认选择的用户信息
const employeeConfirm = (data: any) => {
  formData.value.userId = data.id
  // 最终选择完成后签到或者扫描操作
  checkScan()
}
</script>
