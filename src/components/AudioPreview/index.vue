<script setup lang='ts'>
import { formatImgUrl } from '@/utils/index'

const props = defineProps<{
  url: string
}>()
const isError = ref(false)
const audioRef = ref<HTMLVideoElement>()
const { t } = useI18n()
const handleError = () => {
  isError.value = true
}
const handleRetry = () => {
  audioRef.value?.load()
  isError.value = false
}
</script>

<template>
  <div class="w-full m-10 mt-0 flex items-center justify-center">
    <div v-show="!isError" class="w-full aspect-video flex items-center justify-center">
      <audio ref="audioRef" controls :src="formatImgUrl(props.url)" class="w-full mx-28" @error="handleError"></audio>
    </div>
    <div v-show="isError" class="w-full aspect-video flex flex-col items-center justify-center gap-5 bg-[#2D2F2E] rounded-md">
      <img src="@/assets/images/resource/load-error.png" width="220" />
      <span class="text-base text-white">{{ t('common.uploadLoadingError') }}</span>
      <el-button plain type="primary" class="mt-1" @click="handleRetry">
        {{ t('action.clickRetry') }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang='scss'>

</style>
