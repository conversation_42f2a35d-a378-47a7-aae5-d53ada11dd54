<template>
  <div class="app-main-height">
    <!-- Search Form -->
    <ContentWrap>
      <el-form
        class="-mb-15px search-form"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="120px"
      >
        <el-form-item :label="t('survey.userName')" prop="userName">
          <el-input
            v-model="queryParams.userName"
            :placeholder="t('survey.pleaseEnter') + t('survey.userName')"
            clearable
            @keyup.enter="handleSearch"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item :label="t('examMgt.exam.submitTime')" prop="submitTime">
          <el-date-picker
            v-model="queryParams.submitTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            :start-placeholder="t('survey.startTime')"
            :end-placeholder="t('survey.endTime')"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>

        <el-form-item :label="t('survey.displayRange')" prop="onlyValid">
          <el-select
            v-model="queryParams.onlyValid"
            class="!w-240px"
            clearable
            :placeholder="t('survey.pleaseSelect') + t('survey.displayRange')"
          >
            <el-option :label="t('survey.allRecords')" :value="false" />
            <el-option :label="t('survey.validRecordsOnly')" :value="true" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button @click="handleSearch">
            <Icon icon="ep:search" class="mr-5px" /> {{ t('survey.search') }}
          </el-button>
          <el-button @click="handleReset">
            <Icon icon="ep:refresh" class="mr-5px" /> {{ t('survey.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- Response Data List -->
    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="tableData"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column type="index" :label="t('common.index')" min-width="80" />
        <el-table-column :label="t('survey.userName')" min-width="150">
          <template #default="{ row }">
            <div class="user-name">{{ row.userName }}</div>
          </template>
        </el-table-column>

        <el-table-column :label="t('survey.deptName')" min-width="150">
          <template #default="{ row }">
            <div class="dept-name">{{ row.deptName || '-' }}</div>
          </template>
        </el-table-column>

        <el-table-column
          :label="t('examMgt.exam.submitTime')"
          align="center"
          prop="submitTime"
          width="180"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.submitTime) }}
          </template>
        </el-table-column>

        <el-table-column :label="t('survey.status')" align="center" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="t('survey.isValid')" align="center" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isValid ? 'success' : 'warning'" size="small">
              {{ row.isValid ? t('survey.valid') : t('survey.invalid') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="t('survey.operation')" align="center" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="viewDetail(row)"
            >
              <Icon icon="ep:view" class="mr-5px" />
              {{ t('survey.viewDetail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>

    <!-- 详情弹窗 -->
    <ResponseDetailDialog
      v-model:visible="detailVisible"
      :response-id="currentResponseId"
      @close="detailVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Icon } from '@/components/Icon'
import { ContentWrap } from '@/components/ContentWrap'
// import { Pagination } from '@/components/Pagination'
import ResponseDetailDialog from './ResponseDetailDialog.vue'
import { ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { formatDate } from '@/utils/formatTime'
import { StatisticsApi } from '@/api/system/survey/statistics'

interface Props {
  instanceId: number
}

const props = defineProps<Props>()

// 国际化
const { t } = useI18n()

// 工具函数
const formatDateTime = (dateTime: string | Date) => {
  if (!dateTime) return '-'
  return formatDate(new Date(dateTime))
}

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const detailVisible = ref(false)
const currentResponseId = ref<number | null>(null)
const queryFormRef = ref()

// 查询参数
const queryParams = reactive({
  instanceId: props.instanceId,
  userName: '',
  submitTime: null,
  onlyValid: false,
  status: null,
  pageNo: 1,
  pageSize: 20
})

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      instanceId: queryParams.instanceId,
      userName: queryParams.userName || undefined,
      submitTime: queryParams.submitTime || undefined,
      onlyValid: queryParams.onlyValid,
      status: queryParams.status || undefined,
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize
    }

    // 调用实际的API
    const data = await StatisticsApi.getUserResponseRecords(params)
    tableData.value = data.list || []
    total.value = data.total || 0

    console.log('获取回答数据列表成功', data)
  } catch (error) {
    console.error('获取回答数据失败:', error)
    ElMessage.error(t('survey.loadDataFailed'))
    // 失败时显示空数据
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.pageNo = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.userName = ''
  queryParams.submitTime = null
  queryParams.onlyValid = false
  queryParams.status = null
  queryParams.pageNo = 1
  getList()
}

// 重置方法（保持兼容性）
const handleReset = () => {
  resetQuery()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  // TODO: 实现排序逻辑
  console.log('排序变化:', prop, order)
}

// 查看详情
const viewDetail = (row: any) => {
  currentResponseId.value = row.responseId
  detailVisible.value = true
}

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return 'success'
    case 2:
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态的国际化名称
const getStatusName = (status: number) => {
  switch (status) {
    case 1:
      return t('survey.completed')
    case 2:
      return t('survey.inProgress')
    case 0:
      return t('survey.notStarted')
    default:
      return t('survey.unknown')
  }
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.user-info {
  .user-name {
    font-weight: 500;
    color: #303133;
  }

  .dept-name {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}

.status-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
