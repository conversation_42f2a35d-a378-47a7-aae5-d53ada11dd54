/**
 * 改进后的大文件分片上传逻辑
 * 主要改进点：
 * 1. 完善的错误处理和重试机制
 * 2. 准确的进度计算和状态管理
 * 3. 更好的队列管理和异常处理
 * 4. 详细的日志记录
 */

import { ref } from 'vue'
import axios from 'axios'
import PQueue from 'p-queue'
import * as FileTaskApi from '@/api/infra/fileTask'
import { useMessage } from '@/hooks/web/useMessage'
import type { Part, TaskInfoDTO } from '@/api/infra/fileTask'

// 上传状态枚举
export enum UploadStatus {
  READY = 'ready',
  UPLOADING = 'uploading', 
  SUCCESS = 'success',
  FAIL = 'fail',
  MERGING = 'merging'
}

// 分片上传配置
export interface ChunkUploadConfig {
  maxRetries: number // 最大重试次数
  retryDelay: number // 重试延迟（毫秒）
  timeout: number // 请求超时时间（毫秒）
  concurrency: number // 并发数
}

// 默认配置
const DEFAULT_CONFIG: ChunkUploadConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  timeout: 30000,
  concurrency: 3
}

// 进度信息
export interface ProgressInfo {
  percentage: number
  uploadedSize: number
  totalSize: number
  speed: string
  remainingTime: string
  remainingSize: string
}

/**
 * 改进的分片上传类
 */
export class ImprovedChunkUploader {
  private identifier = ''
  private partQueue: PQueue
  private percentageMap = new Map<number, { totalSize: number; loadedSize: number }>()
  private startTime = 0
  private config: ChunkUploadConfig
  private message = useMessage()

  constructor(config: Partial<ChunkUploadConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.partQueue = new PQueue({ 
      concurrency: this.config.concurrency, 
      autoStart: false 
    })
  }

  /**
   * 上传单个分片（带重试机制）
   */
  private async uploadChunkWithRetry(
    partNumber: number,
    blob: Blob,
    onProgress: (loaded: number, total: number) => void
  ): Promise<void> {
    let retryCount = 0
    
    while (retryCount < this.config.maxRetries) {
      try {
        const preSignUrl = await FileTaskApi.getFileTaskPreSignUrl(this.identifier, partNumber)

        await axios.request({
          url: preSignUrl,
          method: 'PUT',
          data: blob,
          onUploadProgress: (event) => {
            onProgress(event.loaded, event.total || blob.size)
          },
          headers: { 'Content-Type': 'application/octet-stream' },
          timeout: this.config.timeout
        })
        
        console.log(`分片 ${partNumber} 上传成功`)
        return // 成功后直接返回
        
      } catch (error) {
        retryCount++
        console.error(`分片 ${partNumber} 上传失败，重试次数: ${retryCount}/${this.config.maxRetries}`, error)
        
        if (retryCount >= this.config.maxRetries) {
          throw new Error(`分片 ${partNumber} 上传失败，已重试 ${this.config.maxRetries} 次: ${error}`)
        }
        
        // 指数退避重试
        const delay = this.config.retryDelay * Math.pow(2, retryCount - 1)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  /**
   * 更新上传进度
   */
  private updateProgress(
    partNumber: number, 
    totalSize: number, 
    loadedSize: number,
    fileSize: number,
    onProgressUpdate: (progress: ProgressInfo) => void
  ): void {
    this.percentageMap.set(partNumber, { totalSize, loadedSize })
    
    let total = 0
    let loaded = 0
    
    this.percentageMap.forEach((value) => {
      total += value.totalSize
      loaded += value.loadedSize
    })

    if (fileSize === 0) return

    const nowTime = Date.now()
    const intervalTime = (nowTime - this.startTime) / 1000
    
    if (intervalTime <= 0) return
    
    const uploadSpeed = loaded / intervalTime
    const totalRemaining = fileSize - loaded
    const currentProgress = Math.min((loaded / fileSize) * 99, 99) // 上传阶段最多99%
    
    // 计算剩余时间
    let remainingTime = '计算中...'
    if (uploadSpeed > 0 && totalRemaining > 0) {
      const remainingSeconds = Math.ceil(totalRemaining / uploadSpeed)
      if (remainingSeconds < 60) {
        remainingTime = `${remainingSeconds}秒`
      } else if (remainingSeconds < 3600) {
        remainingTime = `${Math.ceil(remainingSeconds / 60)}分钟`
      } else {
        remainingTime = `${Math.ceil(remainingSeconds / 3600)}小时`
      }
    }
    
    const progressInfo: ProgressInfo = {
      percentage: Math.floor(currentProgress),
      uploadedSize: loaded,
      totalSize: fileSize,
      speed: `${(uploadSpeed / 1024 / 1024).toFixed(2)}MB/s`,
      remainingTime,
      remainingSize: `${(totalRemaining / 1024 / 1024).toFixed(1)}MB`
    }
    
    onProgressUpdate(progressInfo)
  }

  /**
   * 执行分片上传
   */
  async uploadChunks(
    file: File,
    taskRecord: TaskInfoDTO,
    onProgressUpdate: (progress: ProgressInfo) => void,
    onStatusChange: (status: UploadStatus) => void
  ): Promise<any> {
    try {
      this.identifier = taskRecord.identifier
      this.startTime = Date.now()
      this.percentageMap.clear()
      
      onStatusChange(UploadStatus.UPLOADING)
      
      let uploadedParts = 0
      const totalParts = taskRecord.chunkNum
      
      // 处理已上传的分片
      for (let partNumber = 1; partNumber <= taskRecord.chunkNum; partNumber++) {
        const existPart = (taskRecord.exitPartList || []).find(
          (part: Part) => part.partNumber === partNumber
        )
        
        if (existPart) {
          // 分片已存在，直接更新进度
          this.updateProgress(partNumber, existPart.size, existPart.size, file.size, onProgressUpdate)
          uploadedParts++
        } else {
          // 添加分片上传任务到队列
          this.partQueue.add(async () => {
            const startSize = taskRecord.chunkSize * (partNumber - 1)
            const endSize = Math.min(startSize + taskRecord.chunkSize, file.size)
            const blob = file.slice(startSize, endSize)
            
            await this.uploadChunkWithRetry(
              partNumber,
              blob,
              (loaded, total) => this.updateProgress(partNumber, total, loaded, file.size, onProgressUpdate)
            )
            
            uploadedParts++
            console.log(`分片上传进度: ${uploadedParts}/${totalParts}`)
          })
        }
      }
      
      // 启动队列并等待所有分片上传完成
      await this.startQueue()
      
      console.log('所有分片上传完成，开始合并文件')
      onStatusChange(UploadStatus.MERGING)
      
      // 合并文件
      const result = await this.mergeFile(file)
      
      // 合并成功，设置100%进度
      onProgressUpdate({
        percentage: 100,
        uploadedSize: file.size,
        totalSize: file.size,
        speed: '0MB/s',
        remainingTime: '0秒',
        remainingSize: '0MB'
      })
      
      onStatusChange(UploadStatus.SUCCESS)
      this.message.success('文件上传成功')
      
      return result
      
    } catch (error) {
      console.error('分片上传失败:', error)
      onStatusChange(UploadStatus.FAIL)
      
      const errorMessage = error instanceof Error ? error.message : '文件上传失败'
      this.message.error(errorMessage)
      throw error
    }
  }

  /**
   * 启动队列
   */
  private async startQueue(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.partQueue
        .start()
        .on('error', (error) => {
          console.error('队列执行出错:', error)
          this.partQueue.clear()
          reject(error)
        })
        .onIdle()
        .then(() => {
          console.log('队列执行完成')
          resolve()
        })
        .catch(reject)
    })
  }

  /**
   * 合并文件
   */
  private async mergeFile(file: File): Promise<any> {
    try {
      const result = await FileTaskApi.mergeFileTask({
        identifier: this.identifier,
        fileType: file.type || 'application/octet-stream',
        digitFlag: false,
        currentFolderId: 0,
        relativePath: file.webkitRelativePath ? 
          file.webkitRelativePath.substring(0, file.webkitRelativePath.lastIndexOf('/')) : ''
      })
      
      console.log('文件合并成功:', result)
      return result
      
    } catch (error) {
      console.error('文件合并失败:', error)
      throw new Error(`文件合并失败: ${error}`)
    }
  }

  /**
   * 取消上传
   */
  cancel(): void {
    this.partQueue.clear()
    this.partQueue.pause()
    console.log('上传已取消')
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.cancel()
    this.percentageMap.clear()
  }
}

/**
 * 使用示例：
 * 
 * const uploader = new ImprovedChunkUploader({
 *   maxRetries: 5,
 *   concurrency: 5
 * })
 * 
 * await uploader.uploadChunks(
 *   file,
 *   taskRecord,
 *   (progress) => {
 *     console.log('进度更新:', progress)
 *     // 更新UI进度
 *   },
 *   (status) => {
 *     console.log('状态变更:', status)
 *     // 更新UI状态
 *   }
 * )
 */
