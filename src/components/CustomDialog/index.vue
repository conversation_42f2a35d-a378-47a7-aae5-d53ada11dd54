<script setup lang='ts' name="CustomDialog">
const props = withDefaults(defineProps<{
  title?: string
  closeOnClickModal?: boolean
  needMini?: boolean
  needMax?: boolean
  autoHeight?: boolean
  destroyOnClose?: boolean
}>(), {
  title: '',
  closeOnClickModal: false,
  needMini: true,
  autoHeight: false,
  needMax: false,
  destroyOnClose: false,
})
const emits = defineEmits<{
  (event: 'close'): void
}>()
const slots = useSlots()
const dialogRef = ref()
const show = ref(false)
const isMini = ref(false)
const isFull = ref(false)
const contentHeight = computed(() => {
  // return [props.autoHeight && !isFull.value ? `h-[calc(100vh-var(--custom-dialog-header)-var(--custom-dialog-mt)-var(--custom-dialog-mb)${slots.footer ? '-var(--custom-dialog-footer)' : ''})]` : undefined, isFull.value ? `h-[calc(100vh-var(--custom-dialog-header)${slots.footer ? '-var(--custom-dialog-footer)' : ''})]` : '']
  const heightStr = {
    height: '',
  }
  if (props.autoHeight && !isFull.value) {
    heightStr.height = `calc(100vh - var(--custom-dialog-header) - var(--custom-dialog-mt) - var(--custom-dialog-mb)${slots.footer ? ' - var(--custom-dialog-footer)' : ''})`
  }
  else if (isFull.value) {
    console.log('改变')

    heightStr.height = `calc(100vh - var(--custom-dialog-header)${slots.footer ? ' - var(--custom-dialog-footer)' : ''})`
  }
  return heightStr
})
const icons = {
  full: 'dialog-fullscreen',
  exitFull: 'dialog-fullscreen-exit',
  mini: 'dialog-minimize',
  close: 'dialog-close',
}
const handleClose = () => {
  emits('close')
}
const handleFullScreen = () => {
  isFull.value = !isFull.value
}
</script>

<template>
  <Dialog
    ref="dialogRef"
    v-model="show"
    :show-close="false"
    class="custom-dialog"
    :class="[isMini ? 'is-mini' : 'custom-mini-dialog', isFullScreen ? 'is-fullscreen' : '']"
    :close-on-click-modal="props.closeOnClickModal"
    :modal="isMini ? false : true"
    :lock-scroll="false"
    :modal-class="isMini ? 'custom-mini-dialog-modal' : undefined"
    :destroy-on-close="props.destroyOnClose"
    v-bind="$attrs"
  >
    <!-- 最小化状态 -->
    <template v-if="isMini" #header>
      <slot v-if="$slots.miniHeader" name="miniHeader"></slot>
      <span v-else class="flex items-center justify-center text-lg" @click="$router.push('/index')">
        {{ props.title }}
      </span>
      <div class="h-full ms-auto flex items-center  ">
        <div class="custom-dialog-action-wrapper group" @click="isMini = false">
          <svg-icon
            :icon-class="icons.full" class="text-lg cursor-pointer group-hover:text-primary"
          />
        </div>
        <div class="custom-dialog-action-wrapper group" @click="handleClose">
          <svg-icon :icon-class="icons.close" class="text-lg cursor-pointer group-hover:text-primary" />
        </div>
      </div>
    </template>
    <!-- 正常状态 -->
    <template v-else #header>
      <slot v-if="$slots.header" name="header"></slot>
      <span v-else class="flex items-center">
        {{ props.title }}
      </span>
      <div class="ms-auto flex">
        <!-- 最小化按钮 -->
        <div v-if="props.needMini" class="custom-dialog-action-wrapper group" @click="isMini = true">
          <svg-icon icon-class="dialog-minimize" class="text-lg cursor-pointer group-hover:text-primary" />
        </div>
        <!-- 全屏按钮 -->
        <div v-if="props.needMax" class="custom-dialog-action-wrapper group" @click="handleFullScreen">
          <svg-icon v-show="isFull" icon-class="dialog-fullscreen-exit" class="text-lg cursor-pointer group-hover:text-primary" />
          <svg-icon v-show="!isFull" icon-class="dialog-fullscreen" class="text-lg cursor-pointer group-hover:text-primary" />
        </div>
        <div class="custom-dialog-action-wrapper group" @click="handleClose">
          <svg-icon icon-class="dialog-close" class="text-lg cursor-pointer group-hover:text-primary" />
        </div>
      </div>
    </template>
    <!-- 内容外部容器 -->
    <!--  v-bind="{ class: contentHeight }" -->
    <div :style="[contentHeight]" class="overflow-y-hidden">
      <slot></slot>
    </div>
    <template v-if="$slots.footer" #footer>
      <slot name="footer"></slot>
    </template>
  </Dialog>
</template>

<style lang='scss'>
html{
  --custom-dialog-width: 280px;
  --custom-dialog-header: 64px;
  --custom-dialog-mt:6vh;
  --custom-dialog-mb:50px;
  --custom-dialog-pt:30px;
  --custom-dialog-pb:50px;
  --custom-dialog-footer:72px;
  --custom-dialog-mini-header-height: 48px;
  --custom-dialog-mini-header-width: 280px;
}
.custom-dialog{

  .el-dialog__body{
    @apply p-[unset];
  }
}

.custom-dialog-action-wrapper {
  @apply size-8 cursor-pointer flex items-center justify-center;
}

.custom-mini-dialog {
  .el-dialog__header {
    @apply p-4 h-[--custom-dialog-header] flex;
  }
}

.is-mini {
  @apply w-[--custom-dialog-width] min-h-11 absolute bottom-0 right-0 #{!important};

  &.el-dialog {
    @apply m-[unset] left-[unset] #{!important};
  }

  .el-dialog__header {
    @apply p-2 h-full flex;
  }

  .el-dialog__body {
    @apply hidden;
  }
  .el-dialog__footer{
    @apply hidden;
  }
}
.custom-mini-dialog-modal{
  @apply top-[calc(100vh-var(--custom-dialog-mini-header-height))] left-[calc(100vw-var(--custom-dialog-mini-header-width))] #{!important};
  .el-overlay-dialog{
    @apply overflow-visible top-[calc(100vh-var(--custom-dialog-mini-header-height))] left-[calc(100vw-var(--custom-dialog-mini-header-width))]
  }
}
</style>
