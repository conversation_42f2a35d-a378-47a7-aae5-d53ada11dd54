import type { TableProps } from 'element-plus'

export interface Scope {
  Company: '1'
  Department: '2'
  Section: '3'
  Position: '4'
  Employee: '5'
}
// 提取 Scope 接口中的所有值并组成联合类型
export interface ScopeData {
  relevanceId: number
  relevanceName: string
  scope: 1 | 2 | 3 | 4 | 5
}

export type ScopeTableProps = Omit<TableProps<ScopeData>, 'data'>

export interface ScopeSelectInstance {
  /** 关闭当前dialog弹框 */
  closeDialog: () => void
  /** 重置搜索 */
  resetSearch: () => void
}
/**
 * confirm事件传递回的数据类型
 */
export interface ScopeConfirm {
  scope: number
  scopes: ScopeData[]
}
