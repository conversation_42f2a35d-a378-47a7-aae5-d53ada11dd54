<script setup lang="ts">
import { useClipboard } from '@vueuse/core'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // The text content to copy
  text: {
    type: String,
    required: true
  },
  // Success message to show after copying
  successMessage: {
    type: String,
    default: 'Copy successful'
  },
  // Duration to show the copied icon state (ms)
  iconStateDuration: {
    type: Number,
    default: 2000
  }
})

const { copy } = useClipboard()
const isCopied = ref(false)

const handleCopy = async () => {
  try {
    await copy(props.text)
    isCopied.value = true
    ElMessage.success(props.successMessage)

    // Reset icon after duration
    setTimeout(() => {
      isCopied.value = false
    }, props.iconStateDuration)
  } catch (error) {
    ElMessage.error('Failed to copy text')
    console.error('Copy failed:', error)
  }
}
</script>

<template>
  <el-button text class="!w-8" @click="handleCopy">
    <Icon :icon="isCopied ? 'ep:check' : 'ep:copy-document'" />
  </el-button>
</template>
