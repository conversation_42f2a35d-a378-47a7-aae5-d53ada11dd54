# Vite Plugin Chunk Split 配置说明

## 概述

本项目已集成 `vite-plugin-chunk-split` 插件，用于优化构建产物的代码分片，提高应用的加载性能。

## 配置位置

配置文件位于：`build/vite/index.ts`

## 当前配置策略

### 1. 基础策略
使用 `split-by-experience` 策略，这是一个基于经验的分片策略，会自动将常见的第三方库进行合理分片。

### 2. 分片大小限制
设置 `chunkSizeWarningLimit: 200`，将单个分片大小限制为 200KB，这有助于：
- 提高首屏加载速度
- 优化缓存效率
- 减少单次网络请求的负载

## 配置选项说明

### strategy 选项
- `default`: 默认策略
- `split-by-experience`: 基于经验的分片策略（当前使用）
- `split-by-size`: 基于文件大小的分片策略

### chunkSizeWarningLimit 选项
- 设置单个分片的大小限制（单位：KB）
- 当前设置：200KB
- 推荐范围：100KB - 500KB

## 性能优化建议

### 1. 分片大小控制
- 单个分片建议控制在 200KB - 500KB 之间
- 避免创建过多小分片，增加 HTTP 请求数

### 2. 缓存策略
- 将变化频率低的库（如 Vue、Element Plus）单独分片
- 业务代码和第三方库分离

### 3. 按需加载
- 配合 Vue Router 的懒加载功能
- 大型组件和功能模块使用动态导入

## 构建验证

### 查看分片结果
构建完成后，可以在 `dist/assets/` 目录下查看生成的 JS 文件：

```bash
pnpm run build:local
ls -la dist/assets/*.js
```

### 分析包大小
推荐使用 `rollup-plugin-visualizer` 或 `webpack-bundle-analyzer` 分析包大小：

```bash
# 安装分析工具
pnpm add -D rollup-plugin-visualizer

# 构建并生成分析报告
pnpm run build:local --report
```

## 常见问题

### Q: 为什么某些库没有被正确分片？
A: 检查库的导入方式，确保使用的是 ES 模块导入，而不是 CommonJS。

### Q: 如何调整分片策略？
A: 修改 `build/vite/index.ts` 中的 `chunkSplitPlugin` 配置，重新构建即可。

### Q: 分片过多会影响性能吗？
A: 是的，过多的分片会增加 HTTP 请求数，建议合理控制分片数量。

## 更新日志

- 2024-07-02: 初始配置，集成 vite-plugin-chunk-split 插件
- 2024-07-02: 优化配置策略
  - 使用 `split-by-experience` 策略
  - 设置单个分片大小限制为 200KB
  - 提高构建性能和加载效率
